package com.nichesolv.evahanam.cache.cacheLoader;

import com.nichesolv.evahanam.cache.service.VehicleCacheRefreshService;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import com.nichesolv.evahanam.vehicleModel.jpa.VehicleModel;
import com.nichesolv.evahanam.vehicleModel.repository.VehicleModelRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Component
public class SetupCacheLoader implements ApplicationListener<ApplicationReadyEvent> {

    @Autowired
    VehicleCacheRefreshService vehicleCacheRefreshService;

    @Autowired
    VehicleModelRepository vehicleModelRepository;

    @Autowired
    VehicleRepository vehicleRepository;

    @Override
    @Transactional
    public void onApplicationEvent(ApplicationReadyEvent event) {
        List<VehicleModel> vehicleModels = vehicleModelRepository.findAll();
        vehicleCacheRefreshService.initializeOrUpdateVehicleModelVariantCache(vehicleModels);
        List<Vehicle> vehicles = vehicleRepository.findAll();
        vehicleCacheRefreshService.initializeOrUpdateImeiToVariantKeyCache(vehicles);
        vehicleCacheRefreshService.initializeOrUpdateVehicleCache(vehicles);
        vehicleCacheRefreshService.initializeOrUpdateAdvertingNameToEncryptionKeyCache(vehicles);
    }
}
