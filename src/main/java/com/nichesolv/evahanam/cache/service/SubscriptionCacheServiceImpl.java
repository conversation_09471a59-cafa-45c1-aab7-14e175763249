package com.nichesolv.evahanam.cache.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.nichesolv.evahanam.cache.enums.KeyType;
import com.nichesolv.evahanam.cache.enums.SubscriptionPlanTypes;
import com.nichesolv.evahanam.common.enums.ParsingFieldType;
import com.nichesolv.evahanam.common.jpa.DataFrequencyPlan;
import com.nichesolv.evahanam.common.jpa.DataParsingField;
import com.nichesolv.evahanam.common.jpa.DataParsingPlan;
import com.nichesolv.evahanam.common.repository.DataFrequencyPlanRepository;
import com.nichesolv.evahanam.common.repository.DataParsingPlanRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SubscriptionCacheServiceImpl implements SubscriptionCacheService {

    @Autowired
    private DataParsingPlanRepository dataParsingPlanRepository;

    @Autowired
    private DataFrequencyPlanRepository dataFrequencyPlanRepository;


    @Autowired
    RedisTemplate<String, Object> redisTemplate;

    static final ObjectMapper mapper = new ObjectMapper().registerModule(new JavaTimeModule());


    static final String dataFreqSubscriptionsKeyType = KeyType.SUBSCRIPTIONS_FREQUENCY.name();
    static final String parsingFieldSubscriptionsKeyType = KeyType.SUBSCRIPTIONS_FIELDS.name();

    static class DataFrequencyPlanDto{
        int value;String unit;
        public DataFrequencyPlanDto(int value, String unit) {
            this.value = value;
            this.unit = unit;
        }
        public int getValue() {
            return value;
        }
        public String getUnit() {
            return unit;
        }
    }

    @Override
    public void initOrUpdateSubscription(String id, KeyType planType) {
        Long planId = Long.valueOf(id);
        if (planType == KeyType.SUBSCRIPTIONS_FIELDS) {

            dataParsingPlanRepository.findById(planId).ifPresentOrElse(plan -> {

                Map<String, List<String>> parsingPlanMap = plan.getFields().stream()
                        .collect(Collectors.groupingBy(
                                field->field.getFieldType().name(), // Group by fieldType
                                Collectors.mapping(DataParsingField::getFieldName, Collectors.toList()) // Map fieldName into a list
                        ));
                String key = parsingFieldSubscriptionsKeyType + "::" + planId;

                try {
                    String vehicleValue = mapper.writeValueAsString(parsingPlanMap);
                    redisTemplate.opsForValue().set(key, vehicleValue);
                    log.info("Successfully updated {} cache for plan: {}",parsingFieldSubscriptionsKeyType, planId);
                } catch (JsonProcessingException e) {
                    log.error("Error serializing {} cache for plan: {} due to {}",parsingFieldSubscriptionsKeyType, planId, e.getMessage(), e);
                }

            }, () -> {
                log.debug("SUBSCRIPTIONS_DATA_FREQUENCY:: Plan not found with id : {}, planType : {}", planId, planType);
            });
        } else if (planType == KeyType.SUBSCRIPTIONS_FREQUENCY) {
            dataFrequencyPlanRepository.findById(planId).ifPresentOrElse(plan -> {

                DataFrequencyPlanDto dataFrequencyPlanMap = new DataFrequencyPlanDto(plan.getValue(),plan.getUnit().name());

                String key = dataFreqSubscriptionsKeyType + "::" + planId;

                try {
                    String vehicleValue = mapper.writeValueAsString(dataFrequencyPlanMap);
                    redisTemplate.opsForValue().set(key, vehicleValue);
                    log.info("Successfully updated {} cache for plan: {}",dataFreqSubscriptionsKeyType, planId);
                } catch (JsonProcessingException e) {
                    log.error("Error serializing {} cache for plan: {} due to {}",dataFreqSubscriptionsKeyType, planId, e.getMessage(), e);
                }

            }, () -> {
                log.debug("SUBSCRIPTIONS_DATA_FREQUENCY:: Plan not found with id : {}, planType : {}", planId, planType);
            });
        }
    }
}
