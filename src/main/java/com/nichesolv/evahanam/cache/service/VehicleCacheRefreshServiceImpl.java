package com.nichesolv.evahanam.cache.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.nichesolv.evahanam.cache.dto.partModelDto.*;
import com.nichesolv.evahanam.cache.dto.vehicle.VehicleDto;
import com.nichesolv.evahanam.cache.dto.vehicle.VehiclePartDto;
import com.nichesolv.evahanam.cache.enums.KeyType;
import com.nichesolv.evahanam.cache.enums.SubscriptionPlanTypes;
import com.nichesolv.evahanam.common.jpa.ActiveVehicleSubscriptionPlan;
import com.nichesolv.evahanam.common.repository.ActiveVehicleSubscriptionPlanRepository;
import com.nichesolv.evahanam.vehicle.dto.VehiclePartModelProjection;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import com.nichesolv.evahanam.vehicleModel.enums.PartType;
import com.nichesolv.evahanam.vehicleModel.jpa.ColorModel;
import com.nichesolv.evahanam.vehicleModel.jpa.PartModel;
import com.nichesolv.evahanam.vehicleModel.jpa.VehicleModel;
import com.nichesolv.evahanam.vehicleModel.repository.PartModelRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class VehicleCacheRefreshServiceImpl implements VehicleCacheRefreshService {

    @Autowired
    RedisTemplate<String, Object> redisTemplate;

    @Autowired
    VehicleRepository vehicleRepository;

    @Autowired
    PartModelRepository partModelRepository;

    @Autowired
    VehicleCacheProcessingService vehicleCacheProcessingService;

    static final ObjectMapper mapper = new ObjectMapper().registerModule(new JavaTimeModule());

    static final String vehicleModelVariantKeyType = KeyType.VEHICLE_MODEL_VARIANT.name();
    static final String imeiVehicleModelVariantKeyType = KeyType.IMEI_VEHICLE_MODEL_VARIANT_KEY.name();
    static final String imeiVehicleKeyType = KeyType.IMEI_VEHICLE.name();
    static final String advertisingNameVehicleKeyType = KeyType.ADVERTISING_NAME_VEHICLE.name();
    static final String vehiclePartsKeyType = KeyType.VEHICLE_PARTS.name();
    static final String vehicleSubscriptionsKeyType = KeyType.VEHICLE_SUBSCRIPTIONS.name();


    /**
     * This is method to create or update cache of variant key to vehicle model variant cache.
     * it will take combination of part models and create variant and construct variant key for variant.
     *
     * @param vehicleModels list of vehicle models
     */
    @Override
    public void initializeOrUpdateVehicleModelVariantCache(List<VehicleModel> vehicleModels) {

        List<VehicleModelVariantDto> vehicleModelDtos = vehicleCacheProcessingService.getVehicleModelVariants(vehicleModels);
        List<VehicleModelVariantDto> vehicleModelsVariants = new ArrayList<>();

        vehicleModelDtos.forEach(vehicleModelDto -> {
//         Step 1: Group the part models by PartType
            Map<PartType, List<PartModelDto>> partModelsByType = new HashMap<>();

            for (PartModelDto partModel : vehicleModelDto.getPartModels()) {
                partModelsByType
                        .computeIfAbsent(partModel.getPartType(), k -> new ArrayList<>())
                        .add(partModel);
            }

            // Step 2: Generate all combinations of part models from each PartType
            List<List<PartModelDto>> allCombinations = vehicleCacheProcessingService.generatePartModelCombinations(new ArrayList<>(partModelsByType.values()));

            // Step 3: Create a new VehicleModel for each combination
            List<VehicleModelVariantDto> variants = new ArrayList<>();
            for (List<PartModelDto> combination : allCombinations) {
                // Create a new VehicleModel with the given combination
                VehicleModelVariantDto newVehicleModelDto = vehicleCacheProcessingService.getVehicleModelVariantCombinations(vehicleModelDto, combination);

                variants.add(newVehicleModelDto);
            }

            vehicleModelsVariants.addAll(variants);

        });

        Map<String, VehicleModelVariantDto> vehicleModelVariantsMap = vehicleModelsVariants.stream()
                .collect(Collectors.toMap(VehicleModelVariantDto::constructKey, obj -> obj, (existing, replacement) -> {
                    return existing;
                }));

        vehicleModelVariantsMap.forEach((key, value) -> {
            String variantKey = vehicleModelVariantKeyType + "::" + key;

            try {
                String variantValue = mapper.writeValueAsString(value);
                redisTemplate.opsForValue().set(variantKey, variantValue);

            } catch (JsonProcessingException e) {
                log.info(e.getMessage());
            }
        });
    }

    /**
     * This is method to create or update cache of imei to vehicle model variant key cache.
     * it will find variant key of given imei and create cache between imei and variant key.
     *
     * @param vehicles list of vehicles
     */
    @Override
    public void initializeOrUpdateImeiToVariantKeyCache(List<Vehicle> vehicles) {
        vehicles.forEach(vehicle -> {
            List<VehiclePartModelProjection> vehiclePartModelProjections = vehicleRepository.getVehiclePartModels(vehicle.getId());
            Set<PartModel> partModels = vehiclePartModelProjections.stream().map((projection) -> {
                return partModelRepository.findById(projection.getId()).orElse(null);
            }).filter(Objects::nonNull).collect(Collectors.toSet());
            partModels.add(vehicle.getColorModel());

            ColorModel colorModel = vehicle.getColorModel();
            PartModelDto colorModelDto = vehicleCacheProcessingService.getPartModels(Set.of(colorModel)).stream().findFirst().get();
            Set<PartModelDto> partModelDtos = vehicle.getVehicleParts().stream().map(part -> {
                return vehicleCacheProcessingService.getPartModels(Set.of(part.getPartModel())).stream().findFirst().get();
            }).collect(Collectors.toSet());
            VehicleModelVariantDto vehicleModelVariantDto = vehicleCacheProcessingService.getVehicleModelVariants(List.of(vehicle.getVehicleModel())).get(0);
            partModelDtos.add(colorModelDto);
            vehicleModelVariantDto.setPartModels(partModelDtos);
            String variantKey = vehicleModelVariantKeyType + "::" + vehicleModelVariantDto.constructKey();
            boolean variantKeyExist = Boolean.TRUE.equals(redisTemplate.hasKey(variantKey));
            if (variantKeyExist) {

                String imeiVehicleModelVariantKey = imeiVehicleModelVariantKeyType + "::".concat(vehicle.getImei());
                redisTemplate.opsForValue().set(imeiVehicleModelVariantKey, variantKey);

            } else {
                log.info("vehicle : {}, vehicle model variant key not exist in vehicle model variant cache : {}", vehicle.getImei(), variantKey);
            }
        });
    }

    /**
     * This is method to create or update cache of adverting name to vehicle.
     *
     * @param vehicles list of vehicles
     */
    @Override
    public void initializeOrUpdateAdvertingNameToEncryptionKeyCache(List<Vehicle> vehicles) {
        vehicles.stream().filter(e -> e.getDeviceAdvertisingName() != null && !e.getDeviceAdvertisingName().isEmpty()).forEach(
                e -> {
                    String key = advertisingNameVehicleKeyType + "::" + e.getDeviceAdvertisingName();
                    String value = e.getEncryptionKey();
                    if (Optional.ofNullable(value).isEmpty() || value.isEmpty()) {
                        log.info("initializeOrUpdateAdvertingNameToEncryptionKeyCache : encryption key is empty for : {}", e.getImei());
                    } else {
                        redisTemplate.opsForValue().set(key, value);

                    }
                }
        );
    }

    /**
     * This is method to evict(delete) imei to vehicle model variant key.
     *
     * @param vehicle vehicle object
     */
    @Override
    public void evictImeiToVariantKeyCache(Vehicle vehicle) {
        String pattern = imeiVehicleModelVariantKeyType + "::" + vehicle.getImei() + "*";
        Set<String> keys = redisTemplate.keys(pattern);

        if (Optional.ofNullable(keys).isPresent() && !keys.isEmpty()) {
            redisTemplate.delete(keys);
            log.info("EVICTED {} with size : {}, vehicleIMEI : {} ", imeiVehicleModelVariantKeyType, keys.size(), vehicle.getImei());

        } else {
            log.info("{} : IMEI {} : can't evict the cache because cache is not exists", imeiVehicleModelVariantKeyType, vehicle.getImei());
        }
    }

    /**
     * This is method to create or update cache of imei to vehicle.
     *
     * @param vehicles list of vehicles
     */
    @Override
    public void initializeOrUpdateVehicleCache(List<Vehicle> vehicles) {
        vehicles.forEach(vehicle -> {
            VehicleDto vehicleDto = vehicleCacheProcessingService.getVehicle(vehicle);
            String key = imeiVehicleKeyType + "::".concat(vehicle.getImei());
            vehicleDto.setSubscriptions(initalizeOrUpdateVehicleSubscriptions(vehicle));

            try {
                String vehicleValue = mapper.writeValueAsString(vehicleDto);
                redisTemplate.opsForValue().set(key, vehicleValue);

            } catch (JsonProcessingException e) {
                log.info(e.getMessage());
            }
        });
    }

    @Override
    public void evictImeiToVehicleCache(Vehicle vehicle) {
        String pattern = imeiVehicleKeyType + "::" + vehicle.getImei() + "*";
        Set<String> keys = redisTemplate.keys(pattern);

        if (Optional.ofNullable(keys).isPresent() && !keys.isEmpty()) {
            redisTemplate.delete(keys);
            log.info("EVICTED {} with size : {},  vehicleIMEI : {} ", imeiVehicleKeyType, keys.size(), vehicle.getImei());

        } else {
            log.info("{} :  IMEI {} : can't evict the cache because cache is not exists", imeiVehicleKeyType, vehicle.getImei());
        }
    }

    @Autowired
    ActiveVehicleSubscriptionPlanRepository vehicleSubscriptionPlanRepository;

    public Map<String, Long> initalizeOrUpdateVehicleSubscriptions(Vehicle vehicle) {
        Map<String, Long> vehicleSubscriptionsMap = new HashMap<>();
        try {
            if (vehicle == null || vehicle.getImei() == null) {
                log.warn("Skipping null or invalid vehicle during subscription initialization.");
                return null;
            }

            Optional<ActiveVehicleSubscriptionPlan> vehicleSubscriptions = vehicleSubscriptionPlanRepository.findByVehicle(vehicle);

            if (vehicleSubscriptions.isEmpty()) {
                log.info("No active subscription plans found for vehicle with IMEI: {}", vehicle.getImei());
            }


            vehicleSubscriptions.ifPresent(sub -> {
                if (sub.getDataParsingPlan() != null) {
                    vehicleSubscriptionsMap.put(KeyType.SUBSCRIPTIONS_FIELDS.name(), sub.getDataParsingPlan().getId());
                } else {
                    log.warn("Data parsing plan is null for vehicle with IMEI: {}", vehicle.getImei());
                }

                if (sub.getDataFrequencyPlan() != null) {
                    vehicleSubscriptionsMap.put(KeyType.SUBSCRIPTIONS_FREQUENCY.name(), sub.getDataFrequencyPlan().getId());
                } else {
                    log.warn("Data frequency plan is null for vehicle with IMEI: {}", vehicle.getImei());
                }
            });

            if (vehicleSubscriptionsMap.isEmpty()) {
                log.info("No valid subscription data found for vehicle with IMEI: {}", vehicle.getImei());
            }
        } catch (Exception e) {
            log.error("Unexpected error while processing vehicle with IMEI: {}. Error: {}",
                    vehicle != null ? vehicle.getImei() : "unknown", e.getMessage(), e);
        }
        return vehicleSubscriptionsMap;

    }

    @Override
    public void initializeOrUpdateVehiclePartCache(List<Vehicle> vehicles) {
        if (vehicles == null || vehicles.isEmpty()) {
            log.warn("No vehicles provided for part cache initialization or update.");
            return;
        }

        vehicles.forEach(vehicle -> {
            try {
                if (vehicle == null || vehicle.getImei() == null) {
                    log.warn("Skipping null or invalid vehicle during part cache initialization.");
                    return;
                }

                VehicleDto vehicleDto = vehicleCacheProcessingService.getVehicle(vehicle);
                if (vehicleDto.getParts() == null || vehicleDto.getParts().isEmpty()) {
                    log.info("No parts found for vehicle with IMEI: {}", vehicle.getImei());
                    return;
                }

                /***
                 * 867461076709951 | {"MOTOR":10278}
                 *  867461076709951 | {"BATTERY":10279}
                 *  867461076657689 | {"MOTOR":10242}
                 *  867461076657689 | {"BATTERY":10243}
                 *  SET 'VEHICLE_PARTS::867461076709951' '{"MOTOR":10278,"BATTERY":10279}'
                 *  SET 'VEHICLE_PARTS::867461076657689' '{"MOTOR":10242,"BATTERY":10243}'
                 */
                String key = vehiclePartsKeyType + "::" + vehicle.getImei();

                try {
                    Map<String, Long> vehicleParts = vehicleDto.getParts().stream().collect(Collectors.toMap(
                            vehiclePart -> vehiclePart.getPartType().name(),
                            VehiclePartDto::getId
                    ));
                    String vehicleValue = mapper.writeValueAsString(vehicleParts);
                    redisTemplate.opsForValue().set(key, vehicleValue);
                    log.info("Successfully updated part cache for vehicle with IMEI: {}", vehicle.getImei());
                } catch (JsonProcessingException e) {
                    log.error("Error serializing part data for vehicle with IMEI: {}. Error: {}", vehicle.getImei(), e.getMessage(), e);
                }

            } catch (Exception e) {
                log.error("Unexpected error while processing vehicle with IMEI: {}. Error: {}",
                        vehicle != null ? vehicle.getImei() : "unknown", e.getMessage(), e);
            }
        });
    }
}
