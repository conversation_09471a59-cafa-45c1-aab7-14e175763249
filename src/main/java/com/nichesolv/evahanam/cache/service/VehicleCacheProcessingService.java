package com.nichesolv.evahanam.cache.service;

import com.nichesolv.evahanam.cache.dto.partModelDto.DriveModeSpeedDto;
import com.nichesolv.evahanam.cache.dto.partModelDto.PartModelDto;
import com.nichesolv.evahanam.cache.dto.partModelDto.VehicleModelVariantDto;
import com.nichesolv.evahanam.cache.dto.vehicle.VehicleDto;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicleModel.jpa.DriveModeSpeed;
import com.nichesolv.evahanam.vehicleModel.jpa.PartModel;
import com.nichesolv.evahanam.vehicleModel.jpa.VehicleModel;

import java.util.List;
import java.util.Set;

public interface VehicleCacheProcessingService {

    Set<PartModelDto> getPartModels(Set<PartModel> partModels);

    VehicleModelVariantDto getVehicleModelVariantCombinations(VehicleModelVariantDto vehicleModelDto, List<PartModelDto> combination);

    List<VehicleModelVariantDto> getVehicleModelVariants(List<VehicleModel> vehicleModelVariants);

    VehicleDto getVehicle(Vehicle vehicle);

    Set<DriveModeSpeedDto> getDriveModeSpeeds(Set<DriveModeSpeed> driveModeSpeeds);

    List<List<PartModelDto>> generatePartModelCombinations(List<List<PartModelDto>> partLists);

}
