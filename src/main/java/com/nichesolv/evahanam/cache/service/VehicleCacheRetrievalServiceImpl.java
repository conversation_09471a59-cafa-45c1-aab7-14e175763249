package com.nichesolv.evahanam.cache.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.nichesolv.evahanam.cache.dto.partModelDto.PartModelDto;
import com.nichesolv.evahanam.cache.dto.partModelDto.VehicleModelVariantDto;
import com.nichesolv.evahanam.cache.enums.KeyType;
import com.nichesolv.evahanam.vehicle.dto.PartModelAttributeProjection;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicleModel.dto.PartModelAttributeDto;
import com.nichesolv.evahanam.vehicleModel.repository.PartModelAttributeRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class VehicleCacheRetrievalServiceImpl implements VehicleCacheRetrievalService {

    @Autowired
    RedisTemplate<String, Object> redisTemplate;


    @Autowired
    PartModelAttributeRepository partModelAttributeRepository;


    static final ObjectMapper mapper = new ObjectMapper().registerModule(new JavaTimeModule());

    static final String imeiVehicleModelVariantKeyType = KeyType.IMEI_VEHICLE_MODEL_VARIANT_KEY.name();

    @Override
    public VehicleModelVariantDto getVehicleModelVariant(String imei) {

        String key = imeiVehicleModelVariantKeyType.concat("::").concat(imei);
        VehicleModelVariantDto vehicleModelVariantDto = new VehicleModelVariantDto();
        try {
            String value = (String) redisTemplate.opsForValue().get(key);

            if (Optional.ofNullable(value).isPresent()) {
                vehicleModelVariantDto = mapper.readValue((String) redisTemplate.opsForValue().get(value), VehicleModelVariantDto.class);
            } else {
                log.info("key : {} is not exists", key);
            }

        } catch (JsonProcessingException e) {
            log.info(e.getMessage());
        }
        return vehicleModelVariantDto;
    }


    @Override
    public List<PartModelAttributeDto> getPartAttributes(String partType, Vehicle vehicle) {
        List<PartModelAttributeDto> partModelAttributeList = new ArrayList<>();
        VehicleModelVariantDto vehicleModelDto = this.getVehicleModelVariant(vehicle.getImei());

        Optional<PartModelDto> partModelDto;
        if (Optional.ofNullable(vehicleModelDto).isPresent()) {
            partModelDto = vehicleModelDto.getPartModels().stream().filter(e -> e.getPartType().name().equals(partType)).findFirst();
        } else {
            partModelDto = Optional.empty();
        }

        if (partModelDto.isPresent()) {
            partModelAttributeList = partModelDto.get().getPartModelAttributes().stream().map(e -> {
                return new PartModelAttributeDto(e.getName(), e.getValue(), partModelDto.get().getId(), e.getType());
            }).toList();
        } else {

            List<PartModelAttributeProjection> partModelAttributeProjections = partModelAttributeRepository.getPartAttributes(partType, vehicle.getId());

            partModelAttributeList = partModelAttributeProjections.stream().map(e -> new PartModelAttributeDto(
                    e.getName(), e.getValue(), e.getModelId(), e.getType()
            )).toList();
        }

        return partModelAttributeList;
    }

    @Override
    public PartModelAttributeDto getAttribute(String attributeName, String partType, Vehicle vehicle) {
        List<PartModelAttributeDto> partModelAttributeDtoList = this.getPartAttributes(partType, vehicle);
        return partModelAttributeDtoList.stream().filter(e -> e.getName().equals(attributeName)).findFirst().orElse(null);
    }
}
