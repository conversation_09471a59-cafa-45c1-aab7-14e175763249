package com.nichesolv.evahanam.cache.service;

import com.nichesolv.evahanam.telemetryData.repository.VehicleDataRepository;
import com.nichesolv.evahanam.telemetryData.dto.GrvDataDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.data.redis.core.RedisTemplate;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class VehicleImeiGrvRawAggCacheRefreshServiceImpl implements VehicleImeiGrvRawAggCacheRefreshService {

    @Autowired
    private VehicleDataRepository vehicleDataRepository;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Value("${spring.cache.redis.grv-raw-agg-prefix}")
    String GRV_RAW_AGG_CACHE_KEY_PREFIX;

    @Value("${spring.cache.redis.ttl-grv-agg-hours}")
    int TIME_TO_LIVE_HOURS;

    @Value("${spring.cache.redis.grv-null-placeholder}")
    String NULL_PLACEHOLDER;

    /**
     * Handles the scenario where the cache is completely missing for the given IMEI.
     *
     * @param imei The IMEI of the vehicle.
     */
    public void handleGrvRawAggregateMiss(String imei) {
        String redisKey = GRV_RAW_AGG_CACHE_KEY_PREFIX + imei;

        if (Boolean.TRUE.equals(redisTemplate.hasKey(redisKey))) {
            log.debug("Cache exists for IMEI: {}. No action taken in handleGrvAggregateMiss.", imei);
            return;
        }

        Optional<GrvDataDto> grvRawAggregateOptional = vehicleDataRepository.getLatestGrvRawAggregate(imei);

        if (grvRawAggregateOptional.isPresent()) {
            GrvDataDto grvRawAggregate = grvRawAggregateOptional.get();
            log.debug("Cache miss: Found GRV Aggregate for IMEI {}: X={}, Y={}, Z={}",
                    imei, grvRawAggregate.getXRunning(), grvRawAggregate.getYRunning(), grvRawAggregate.getZRunning());
            redisTemplate.opsForValue().set(redisKey, grvRawAggregate, TIME_TO_LIVE_HOURS, TimeUnit.HOURS);
        } else {
            log.debug("Cache miss: No GRV Aggregate found for IMEI: {}. Setting placeholder.", imei);
            redisTemplate.opsForValue().set(redisKey, NULL_PLACEHOLDER, 5, TimeUnit.MINUTES); // Shorter TTL for re-check
        }
    }

    /**
     * Handles the scenario where the cache for the given IMEI has expired.
     *
     * @param imei The IMEI of the vehicle.
     */
    public void handleGrvRawAggregateExpire(String imei) {
        String redisKey = GRV_RAW_AGG_CACHE_KEY_PREFIX + imei;

        // Fetch and refresh the cache
        Optional<GrvDataDto> grvAggregateOptional = vehicleDataRepository.getLatestGrvRawAggregate(imei);

        if (grvAggregateOptional.isPresent()) {
            GrvDataDto grvAggregate = grvAggregateOptional.get();
            log.debug("Cache expired: Found GRV Aggregate for IMEI {}: X={}, Y={}, Z={}",
                    imei, grvAggregate.getXRunning(), grvAggregate.getYRunning(), grvAggregate.getZRunning());

            // Refresh the GRV Aggregate with a TTL of 1 minute
            redisTemplate.opsForValue().set(redisKey, grvAggregate, TIME_TO_LIVE_HOURS, TimeUnit.HOURS);
        } else {
            log.debug("Cache expired: No GRV Aggregate found for IMEI: {}", imei);
        }
    }
}
