package com.nichesolv.evahanam.cache.service;

import com.nichesolv.evahanam.cache.dto.partModelDto.VehicleModelVariantDto;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicleModel.dto.PartModelAttributeDto;

import java.util.List;

public interface VehicleCacheRetrievalService {

    VehicleModelVariantDto getVehicleModelVariant(String imei);

    List<PartModelAttributeDto> getPartAttributes(String partType, Vehicle vehicle);

    PartModelAttributeDto getAttribute(String attributeName, String partType, Vehicle vehicle);
}
