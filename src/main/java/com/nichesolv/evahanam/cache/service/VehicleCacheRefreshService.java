package com.nichesolv.evahanam.cache.service;

import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicleModel.jpa.VehicleModel;

import java.util.List;

public interface VehicleCacheRefreshService {

    void initializeOrUpdateVehicleModelVariantCache(List<VehicleModel> vehicleModels);

    void initializeOrUpdateImeiToVariantKeyCache(List<Vehicle> vehicles);

    void initializeOrUpdateAdvertingNameToEncryptionKeyCache(List<Vehicle> vehicles);

    void evictImeiToVariantKeyCache(Vehicle vehicle);

    void initializeOrUpdateVehicleCache(List<Vehicle> vehicles);

    void evictImeiToVehicleCache(Vehicle vehicle);

    void initializeOrUpdateVehiclePartCache(List<Vehicle> vehicle);
}
