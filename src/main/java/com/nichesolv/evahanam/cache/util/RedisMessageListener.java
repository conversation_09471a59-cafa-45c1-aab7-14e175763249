package com.nichesolv.evahanam.cache.util;

import com.nichesolv.evahanam.cache.handler.EventHandler;
import com.nichesolv.evahanam.cache.handler.EventHandlerFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@Slf4j
public class RedisMessageListener implements MessageListener {
    @Autowired
    private EventHandlerFactory eventHandlerFactory;


    @Override
    @Transactional
    public void onMessage(Message message, byte[] pattern) {

        try {
            String channel = new String(message.getChannel());
            String body = new String(message.getBody());

            log.info("Received message from channel {}: {}", channel, body);

            String[] keyValueArray = body.split("::");
            String[] channelArray = channel.split(":");
            String eventName = channelArray[channelArray.length - 1];
            String keyType = keyValueArray[0];

            EventHandler handler = eventHandlerFactory.getHandler(keyType);
            if (handler != null) {
                handler.handleEvent(keyValueArray, eventName);
            } else {
                log.info("No handler found for key type: {}", keyType);
            }


        } catch (Exception e) {
            log.error("Error occurred while processing Redis message: {}", message.toString(), e);
        }
    }
}
