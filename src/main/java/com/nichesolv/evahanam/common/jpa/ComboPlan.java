package com.nichesolv.evahanam.common.jpa;

import com.nichesolv.evahanam.common.enums.SubscriptionPlanStatus;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@AllArgsConstructor
@NoArgsConstructor
@Data
@Table(uniqueConstraints = {
        @UniqueConstraint(columnNames = {"name"})
})
public class ComboPlan {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    Long id;

    @Column(unique = true)
    String name;

    @ManyToOne
    DataFrequencyPlan dataFrequencyPlan;

    @ManyToOne
    DataParsingPlan dataParsingPlan;

    @Enumerated(EnumType.STRING)
    SubscriptionPlanStatus status;

    //here some other plans may come up in the future -> DataFieldPlans, DataAnalysisPlans

    public ComboPlan(String name, DataFrequencyPlan dataFrequencyPlan, DataParsingPlan dataParsingPlan, SubscriptionPlanStatus status) {
        this.name = name;
        this.dataFrequencyPlan = dataFrequencyPlan;
        this.dataParsingPlan = dataParsingPlan;
        this.status = status;
    }
}
