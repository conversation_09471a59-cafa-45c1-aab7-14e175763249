package com.nichesolv.evahanam.common.jpa;

import com.nichesolv.evahanam.common.enums.ParsingFieldType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;


@NoArgsConstructor
@Entity
@Data
@Table(uniqueConstraints = {
        @UniqueConstraint(name = "uk_field_name_type",columnNames = {"field_name", "field_type"})
})
public class DataParsingField {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE,generator = "data_parsing_field_seq_generator")
    @SequenceGenerator(name = "data_parsing_field_seq_generator", sequenceName = "data_parsing_field_seq",allocationSize = 1)
    Long id;

    @Column(name = "field_name")
    String fieldName;

    @Enumerated(EnumType.STRING)
    @Column(name = "field_type")
    ParsingFieldType fieldType;

    public DataParsingField(String fieldName, ParsingFieldType fieldType) {
        this.fieldName = fieldName;
        this.fieldType = fieldType;
    }
}
