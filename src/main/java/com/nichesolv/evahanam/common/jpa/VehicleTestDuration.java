package com.nichesolv.evahanam.common.jpa;

import com.nichesolv.evahanam.vehicleTests.enums.TestTypeName;
import com.nichesolv.evahanam.vehicleTests.jpa.TestType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.temporal.ChronoUnit;

@Entity
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class VehicleTestDuration {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE , generator = "vehicle_test_duration_seq")
    @SequenceGenerator(name = "vehicle_test_duration_seq" , sequenceName = "vehicle_test_duration_seq" , allocationSize = 1)
    Long id;

    @Enumerated(EnumType.STRING)
    TestTypeName testTypeName;

    @OneToOne
    @JoinColumn(name = "data_frequency_plan_id")
    DataFrequencyPlan dataFrequencyPlan;

    Integer duration;

    @Enumerated(EnumType.STRING)
    ChronoUnit unit;
}
