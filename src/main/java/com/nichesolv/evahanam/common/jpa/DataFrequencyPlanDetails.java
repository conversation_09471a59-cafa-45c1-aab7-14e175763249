package com.nichesolv.evahanam.common.jpa;

import com.nichesolv.evahanam.common.enums.FeatureName;
import com.nichesolv.evahanam.common.enums.SubscriptionPlanStatus;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.temporal.ChronoUnit;
import java.util.Set;

@Entity
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Table(uniqueConstraints = {
        @UniqueConstraint(name = "uk_data_frequency_plan_feature_name",columnNames = {"data_frequency_plan_id", "featureName"})
})
public class DataFrequencyPlanDetails {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "data_frequency_plan_details_seq_generator")
    @SequenceGenerator(name = "data_frequency_plan_details_seq_generator", sequenceName = "data_frequency_plan_details_seq", allocationSize = 1)
    Long id;

    @Enumerated(EnumType.STRING)
    @NotNull
    FeatureName featureName;

    @ManyToOne
    @JoinColumn(name = "data_frequency_plan_id", foreignKey = @ForeignKey(name = "fk_data_frequency_plan_id"))
    DataFrequencyPlan dataFrequencyPlan;

    @NotNull
    @Enumerated(EnumType.STRING)
    SubscriptionPlanStatus status;

    @NotNull
    Short computationFrequency;

    @NotNull
    @Enumerated(EnumType.STRING)
    ChronoUnit unit;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "data_frequency_plan_details_aggregate_names",
            joinColumns = {@JoinColumn(name = "data_frequency_plan_details_id", nullable = false, foreignKey = @ForeignKey(name = "fk_data_frequency_plan_details_id"))}
            , inverseJoinColumns = {@JoinColumn(name = "aggregate_name_id", nullable = false, foreignKey = @ForeignKey(name = "fk_aggregate_name_id"))})
    Set<AggregateName> aggregateName;
}
