package com.nichesolv.evahanam.common.jpa;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.nichesolv.evahanam.vehicleModel.jpa.VehicleModel;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.OneToOne;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class VehicleModelSubscription extends Subscription{

    @OneToOne
    @NotNull
    @JoinColumn(name = "vehicle_model_id")
    VehicleModel vehicleModel;
}
