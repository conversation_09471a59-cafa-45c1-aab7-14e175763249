package com.nichesolv.evahanam.common.jpa;

import com.nichesolv.evahanam.common.enums.PlanStatus;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.HashSet;
import java.util.Set;


@NoArgsConstructor
@Entity
@Getter
@Setter
@AllArgsConstructor
public class DataParsingPlan {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY,generator = "data_parsing_plan_seq_generator")
    @SequenceGenerator(name = "data_parsing_plan_seq_generator", sequenceName = "data_parsing_plan_seq",allocationSize = 1)
    Long id;

    @NotEmpty
    @Column(unique = true)
    String name;

    String description;

    @Enumerated(EnumType.STRING)
    PlanStatus planStatus;


    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "data_parsing_plan_fields",
            joinColumns = {@JoinColumn(name = "data_parsing_plan_id", nullable = false, foreignKey = @ForeignKey(name = "fk_data_parsing_plan_id"))}
            , inverseJoinColumns = {@JoinColumn(name = "field_id", nullable = false, foreignKey = @ForeignKey(name = "fk_field_id"))})
    Set<DataParsingField> fields = new HashSet<>();

    public DataParsingPlan(String name, String description, PlanStatus planStatus, Set<DataParsingField> fields) {
        this.name = name;
        this.description = description;
        this.planStatus = planStatus;
        this.fields = fields;
    }
}
