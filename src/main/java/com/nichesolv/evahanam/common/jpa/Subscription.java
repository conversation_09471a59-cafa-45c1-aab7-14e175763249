package com.nichesolv.evahanam.common.jpa;

import com.nichesolv.nds.model.organisation.CustomOrganisation;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
@MappedSuperclass
public class Subscription {


    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    Long id;

    @ManyToOne
    @NotNull
    ComboPlan comboPlan;

    Instant startDate;

    Instant endDate;
}
