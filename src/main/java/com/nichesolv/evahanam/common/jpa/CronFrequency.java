package com.nichesolv.evahanam.common.jpa;


import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.temporal.ChronoUnit;
import java.util.Set;


@Entity
@AllArgsConstructor
@NoArgsConstructor
@Data
@Table(uniqueConstraints = {
        @UniqueConstraint(name = "uk_cron_time_unit",columnNames = {"cronTime", "unit"})
})
public class CronFrequency {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "cron_frequency_seq_generator")
    @SequenceGenerator(name = "cron_frequency_seq_generator", sequenceName = "cron_frequency_seq", allocationSize = 1)
    Long id;

    @NotNull
    Integer cronTime;

    @NotNull
    @Enumerated(EnumType.STRING)
    ChronoUnit unit;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "cron_frequency_data_frequency_plan_details",
            joinColumns = {@JoinColumn(name = "cron_frequency_id", nullable = false, foreignKey = @ForeignKey(name = "fk_cron_frequency_id"))}
            , inverseJoinColumns = {@JoinColumn(name = "data_frequency_plan_details_id", nullable = false, foreignKey = @ForeignKey(name = "fk_data_frequency_plan_details_id"))})
    Set<DataFrequencyPlanDetails> dataFrequencyPlanDetails;
}
