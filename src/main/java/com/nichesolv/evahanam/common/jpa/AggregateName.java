package com.nichesolv.evahanam.common.jpa;

import com.nichesolv.evahanam.common.enums.EntityType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AggregateName {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE,generator = "aggregate_name_seq_generator")
    @SequenceGenerator(name = "aggregate_name_seq_generator", sequenceName = "aggregate_name_seq",allocationSize = 1)
    Long id;

    @NotNull
    String name;

    @Enumerated(EnumType.STRING)
    EntityType type;
}
