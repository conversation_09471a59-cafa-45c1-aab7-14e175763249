package com.nichesolv.evahanam.common.jpa;

import com.nichesolv.evahanam.common.enums.CommentType;
import com.nichesolv.evahanam.vehicleTests.jpa.VehicleTest;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@Entity
public class Comment {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    Long id;
    LocalDateTime createdOn;
    LocalDateTime modifiedOn;
    /**
     * update to user class when available
     */
    Long userId;
    String expectedValue;
    String key;
    String value;
    @Enumerated(EnumType.STRING)
    CommentType commentType;
    @ManyToOne
    @JoinColumn(foreignKey = @ForeignKey(name = "fk_vehicle_test_id"))
    VehicleTest vehicleTest;
    @Column(length = 2000)
    String comment;

    public Comment(String comment) {
        this.comment = comment;
    }
}
