package com.nichesolv.evahanam.common.jpa;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Image {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    Long id;
    String url;

    @CreationTimestamp
    LocalDateTime createdAt;
    String tag;
    String description;

    public Image(String url, LocalDateTime createdAt, String tag) {
        this.url = url;
        this.createdAt = createdAt;
        this.tag = tag;
    }
}
