package com.nichesolv.evahanam.common.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Instant;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SubscriptionDto {

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    Long id;

    @NotNull
    Long comboPlanId;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    String comboPlanName;

    @NotNull
    Long startDate;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    Long endDate;

}
