package com.nichesolv.evahanam.common.dto;
import com.nichesolv.evahanam.common.enums.PlanStatus;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Set;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DataParsingPlanDto {

    Long id;

    @NotBlank(message = "name can't be blank")
    String name;

    String description;

    @NotNull(message = "status can't be null")
    PlanStatus status;

    Set<DataParsingFieldDto> fields;

    public DataParsingPlanDto(String name, String description, PlanStatus status, Set<DataParsingFieldDto> fields) {
        this.name = name;
        this.description = description;
        this.status = status;
        this.fields = fields;
    }
}
