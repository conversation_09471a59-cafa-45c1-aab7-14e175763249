package com.nichesolv.evahanam.common.dto;

import com.nichesolv.evahanam.common.enums.SubscriptionPlanStatus;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Optional;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ComboPlanPostRequestDto {

    Long id;

    @NotBlank(message = "Name can't not be blank")
    String name;

    @NotNull(message = "Status can't be null")
    @Enumerated(EnumType.STRING)
    SubscriptionPlanStatus status;

    Long dataFrequencyPlanId;

    Long dataParsingPlanId;

    public ComboPlanPostRequestDto(String name, SubscriptionPlanStatus status, Long dataFrequencyPlanId, Long dataParsingPlanId) {
        this.name = name;
        this.status = status;
        this.dataFrequencyPlanId = dataFrequencyPlanId;
        this.dataParsingPlanId = dataParsingPlanId;
    }
}
