package com.nichesolv.evahanam.common.dto;

import com.nichesolv.evahanam.common.enums.SubscriptionPlanStatus;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ComboPlanDto {

    Long id;

    @NotBlank(message = "Name can't not be blank")
    String name;

    @NotNull(message = "Status can't be null")
    @Enumerated(EnumType.STRING)
    SubscriptionPlanStatus status;

    DataFrequencyPlanDto dataFrequencyPlanDto;

    DataParsingPlanDto dataParsingPlanDto;

    public ComboPlanDto(String name, SubscriptionPlanStatus status, DataFrequencyPlanDto dataFrequencyPlanDto, DataParsingPlanDto dataParsingPlanDto) {
        this.name = name;
        this.status = status;
        this.dataFrequencyPlanDto = dataFrequencyPlanDto;
        this.dataParsingPlanDto = dataParsingPlanDto;
    }
}
