package com.nichesolv.evahanam.common.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DataFrequencyPlanDto {

    Long id;

    @NotBlank(message = "name can't be blank")
    String name;

    @NotNull(message = "value can't be null")
    Integer value;

    @NotBlank(message = "unit can't be blank")
    String unit;

    public DataFrequencyPlanDto(String name, Integer value, String unit) {
        this.name = name;
        this.value = value;
        this.unit = unit;
    }
}
