package com.nichesolv.evahanam.common.dto;

import com.nichesolv.evahanam.common.enums.ParsingFieldType;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataParsingFieldDto {

    Long id;

    String fieldName;

    @Enumerated(EnumType.STRING)
    ParsingFieldType fieldType;
}
