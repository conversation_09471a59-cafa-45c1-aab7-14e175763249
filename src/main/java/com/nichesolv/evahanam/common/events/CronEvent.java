package com.nichesolv.evahanam.common.events;

import com.nichesolv.evahanam.vehicle.dto.CronDataSource;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEvent;


@Getter
@Setter
@Slf4j
public class CronEvent extends ApplicationEvent {

    CronDataSource source;

    public CronEvent(CronDataSource source) {
        super(source);
        this.source = source;
    }
}
