package com.nichesolv.evahanam.common.events;

import com.nichesolv.evahanam.common.jpa.DataFrequencyPlanDetails;
import com.nichesolv.evahanam.vehicle.enums.VehicleState;
import com.nichesolv.evahanam.vehicle.jpa.VehicleStatus;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEvent;

import java.time.Instant;


@Getter
@Slf4j
public class VehicleEvent extends ApplicationEvent {

    @NotNull
    String imei;
    @NotNull
    Instant time;
    @NotNull
    VehicleState state;
    @NotNull
    DataFrequencyPlanDetails dataFrequencyPlanDetails;

    public VehicleEvent(String imei,Instant time, DataFrequencyPlanDetails dataFrequencyPlanDetails,VehicleState vehicleState) {
        super(imei);
        this.imei = imei;
        this.time = time;
        this.state = vehicleState;
        this.dataFrequencyPlanDetails = dataFrequencyPlanDetails;
    }
}
