package com.nichesolv.evahanam.common.repository;

import com.nichesolv.evahanam.common.jpa.CronFrequency;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;

@Repository
public interface CronFrequencyRepository extends JpaRepository<CronFrequency,Long> {

    Optional<CronFrequency> findByCronTimeAndUnit(Integer cronTime, ChronoUnit unit);
    List<CronFrequency> findByCronTimeInAndUnitIn(List<Integer> cronTime,List<ChronoUnit> unit);
}
