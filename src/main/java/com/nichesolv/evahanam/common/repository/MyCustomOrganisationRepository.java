package com.nichesolv.evahanam.common.repository;

import com.nichesolv.nds.dto.organisation.enums.OrganisationType;
import com.nichesolv.nds.model.organisation.CustomOrganisation;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface MyCustomOrganisationRepository extends JpaRepository<CustomOrganisation, Long> {

  Optional<CustomOrganisation> findByOrganisationProfileId(Long id);

  @Query(value = "select organisation_type from organisation_users ou, organisations o where ou.organisation_id=o.id and user_id=?1",nativeQuery = true)
  List<String> findOrganisationTypeByUserId(Long userId);
}
