package com.nichesolv.evahanam.common.repository;

import com.nichesolv.evahanam.common.jpa.DataFrequencyPlan;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface DataFrequencyPlanRepository extends JpaRepository<DataFrequencyPlan,Long> {

    Optional<DataFrequencyPlan> findByName(String name);
}
