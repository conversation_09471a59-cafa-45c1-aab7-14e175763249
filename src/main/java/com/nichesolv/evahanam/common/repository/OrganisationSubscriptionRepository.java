package com.nichesolv.evahanam.common.repository;
import com.nichesolv.evahanam.common.jpa.OrganisationSubscription;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface OrganisationSubscriptionRepository extends JpaRepository<OrganisationSubscription,Long> {

    boolean existsByOrganisation(CustomOrganisation organisation);

    boolean existsByOrganisationAndEndDateNull(CustomOrganisation organisation);

    List<OrganisationSubscription> findByOrganisation(CustomOrganisation organisation);

    Optional<OrganisationSubscription> findByOrganisationAndEndDateNull(CustomOrganisation organisation);

}
