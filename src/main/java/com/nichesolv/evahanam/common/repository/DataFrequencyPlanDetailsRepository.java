package com.nichesolv.evahanam.common.repository;


import com.nichesolv.evahanam.common.enums.FeatureName;
import com.nichesolv.evahanam.common.enums.SubscriptionPlanStatus;
import com.nichesolv.evahanam.common.jpa.DataFrequencyPlan;
import com.nichesolv.evahanam.common.jpa.DataFrequencyPlanDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DataFrequencyPlanDetailsRepository extends JpaRepository<DataFrequencyPlanDetails,Long> {

    DataFrequencyPlanDetails findByFeatureNameAndStatusAndComputationFrequencyAndUnit(String featureName, SubscriptionPlanStatus featureStatus, Short computationFrequency, String unit);

    Optional<DataFrequencyPlanDetails> findByFeatureNameAndDataFrequencyPlan(FeatureName featureName, DataFrequencyPlan dataFrequencyPlan);
}
