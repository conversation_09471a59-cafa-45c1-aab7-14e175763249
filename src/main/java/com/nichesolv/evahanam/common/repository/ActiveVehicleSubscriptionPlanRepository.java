package com.nichesolv.evahanam.common.repository;

import com.nichesolv.evahanam.common.jpa.ActiveVehicleSubscriptionPlan;
import com.nichesolv.evahanam.common.jpa.ComboPlan;
import com.nichesolv.evahanam.common.jpa.DataFrequencyPlan;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import jakarta.persistence.QueryHint;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

import static org.hibernate.jpa.HibernateHints.HINT_FETCH_SIZE;

@Repository
public interface ActiveVehicleSubscriptionPlanRepository extends JpaRepository<ActiveVehicleSubscriptionPlan,Long> {

    @QueryHints(value = {
            @QueryHint(name = HINT_FETCH_SIZE, value = "500"),
    })
    Stream<ActiveVehicleSubscriptionPlan> findAllByDataFrequencyPlan(DataFrequencyPlan dataFrequencyPlan);

    Optional<ActiveVehicleSubscriptionPlan> findByVehicle(Vehicle vehicle);

    List<ActiveVehicleSubscriptionPlan> findByComboPlan(ComboPlan comboPlan);

    @Query(value = "SELECT avsp.* FROM active_vehicle_subscription_plan avsp JOIN vehicle v ON avsp.vehicle_id = v.id AND v.mfr_org_id = ?1", nativeQuery = true)
    List<ActiveVehicleSubscriptionPlan> findVehiclesByOrganisation(Long orgId);


    @Query(value = "SELECT avsp.* FROM active_vehicle_subscription_plan avsp JOIN vehicle v ON avsp.vehicle_id = v.id AND v.vehicle_model_id =?1" , nativeQuery = true)
    List<ActiveVehicleSubscriptionPlan> findVehiclesByVehicleModel(Long vehicleModelId);
}
