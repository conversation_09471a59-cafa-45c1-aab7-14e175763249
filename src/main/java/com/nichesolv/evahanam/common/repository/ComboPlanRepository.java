package com.nichesolv.evahanam.common.repository;

import com.nichesolv.evahanam.common.enums.SubscriptionPlanStatus;
import com.nichesolv.evahanam.common.jpa.ComboPlan;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ComboPlanRepository extends JpaRepository<ComboPlan,Long> {
    Optional<ComboPlan> findByName(String name);

    List<ComboPlan> findByStatus(SubscriptionPlanStatus status);
}
