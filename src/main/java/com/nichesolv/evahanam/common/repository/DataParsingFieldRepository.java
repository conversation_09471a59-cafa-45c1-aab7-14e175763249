package com.nichesolv.evahanam.common.repository;

import com.nichesolv.evahanam.common.enums.ParsingFieldType;
import com.nichesolv.evahanam.common.jpa.DataParsingField;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DataParsingFieldRepository  extends JpaRepository<DataParsingField,Long> {

    List<DataParsingField> findByFieldType(ParsingFieldType fieldType);

    Optional<DataParsingField> findByFieldTypeAndFieldName(ParsingFieldType fieldType,String fieldName);
}
