package com.nichesolv.evahanam.common.repository;

import com.nichesolv.evahanam.common.jpa.VehicleModelSubscription;
import com.nichesolv.evahanam.vehicleModel.jpa.VehicleModel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface VehicleModelSubscriptionRepository extends JpaRepository<VehicleModelSubscription,Long> {

    boolean existsByVehicleModel(VehicleModel vehicleModel);

    boolean existsByVehicleModelAndEndDateNull(VehicleModel vehicleModel);

    List<VehicleModelSubscription> findByVehicleModel(VehicleModel vehicleModel);

    Optional<VehicleModelSubscription> findByVehicleModelAndEndDateNull(VehicleModel vehicleModel);

}
