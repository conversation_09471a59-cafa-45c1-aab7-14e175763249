package com.nichesolv.evahanam.common.repository;

import com.nichesolv.evahanam.common.jpa.DataParsingPlan;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface DataParsingPlanRepository extends JpaRepository<DataParsingPlan,Long> {

    Optional<DataParsingPlan> findByName(String name);
}
