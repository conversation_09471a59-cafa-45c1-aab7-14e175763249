package com.nichesolv.evahanam.common.repository;

import com.nichesolv.evahanam.common.jpa.DataFrequencyPlan;
import com.nichesolv.evahanam.common.jpa.VehicleTestDuration;
import com.nichesolv.evahanam.vehicleTests.enums.TestTypeName;
import org.springframework.data.jpa.repository.JpaRepository;
import java.util.Optional;

public interface VehicleTestDurationRepository extends JpaRepository<VehicleTestDuration, Long> {
    Optional<VehicleTestDuration> findByTestTypeNameAndDataFrequencyPlan(TestTypeName testTypeName, DataFrequencyPlan dataFrequencyPlan);
}



