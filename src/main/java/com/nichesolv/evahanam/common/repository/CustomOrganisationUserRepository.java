package com.nichesolv.evahanam.common.repository;

import com.nichesolv.nds.model.user.CustomUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CustomOrganisationUserRepository extends JpaRepository<CustomUser, Long> {

    @Query(value = "SELECT u.* FROM users u " +
            "JOIN organisation_users ou ON u.id = ou.user_id " +
            "WHERE ou.organisation_id = :organisationId AND u.id IN (:userIds)", nativeQuery = true)
    List<CustomUser> findByIdsInAndOrganisationId(List<Long> userIds, Long organisationId);
}