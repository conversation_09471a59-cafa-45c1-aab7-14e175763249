package com.nichesolv.evahanam.common.controller;

import com.nichesolv.evahanam.common.annotations.ReadOnly;
import com.nichesolv.evahanam.common.dto.CommentDto;
import com.nichesolv.evahanam.common.service.CommentService;
import com.nichesolv.evahanam.util.HttpRequestOriginUtil;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/comments")
@SecurityRequirement(name = "Bearer Authentication")
public class CommentController {
    @Autowired
    CommentService commentService;
    @Autowired
    HttpRequestOriginUtil httpRequestOriginUtil;

    @PostMapping
    public CommentDto saveComment(@Validated @RequestBody CommentDto commentDto, HttpServletRequest request) {
        CustomOrganisation organisation = httpRequestOriginUtil.getUserOrganisation(request);
        return commentService.saveComment(commentDto,organisation);
    }

    @GetMapping
    @ReadOnly
    public List<CommentDto> findByVehicleTestId(@RequestParam Long vehicleTestId) {
        return commentService.findCommentsByVehicleTestId(vehicleTestId);
    }
}
