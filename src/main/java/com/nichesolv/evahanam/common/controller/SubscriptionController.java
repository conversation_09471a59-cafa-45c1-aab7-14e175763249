package com.nichesolv.evahanam.common.controller;

import com.nichesolv.evahanam.common.annotations.ReadOnly;
import com.nichesolv.evahanam.common.dto.OrganisationSubscriptionDto;
import com.nichesolv.evahanam.common.dto.SubscriptionDto;
import com.nichesolv.evahanam.common.dto.VehicleModelSubscriptionDto;
import com.nichesolv.evahanam.common.service.SubscriptionService;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/subscriptions")
@Slf4j
@SecurityRequirement(name = "Bearer Authentication")
public class SubscriptionController {

    @Autowired
    SubscriptionService subscriptionService;


    @GetMapping("/organisations/{id}")
    @ReadOnly
    public ResponseEntity<List<OrganisationSubscriptionDto>> getOrganisationSubscriptionByOrgId(@PathVariable Long id) {
        List<OrganisationSubscriptionDto> organisationSubscriptionDtoList = subscriptionService.getOrganisationSubscriptionByOrg(id);
        return ResponseEntity.ok(organisationSubscriptionDtoList);
    }

    @GetMapping("vehicle-models/{id}")
    @ReadOnly
    public ResponseEntity<List<VehicleModelSubscriptionDto>> getVehicleModelSubscriptionByVehicleModelId(@PathVariable Long id) {
        List<VehicleModelSubscriptionDto> vehicleModelSubscriptionDtoList = subscriptionService.getVehicleModelSubscriptionByVehicleModelId(id);
        return ResponseEntity.ok(vehicleModelSubscriptionDtoList);

    }

    @GetMapping("/{id}")
    @ReadOnly
    public ResponseEntity<SubscriptionDto> getSubscription(@PathVariable Long id) {
        return subscriptionService.getSubscription(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @PutMapping("vehicle-models")
    public ResponseEntity<VehicleModelSubscriptionDto> update(@Valid @RequestBody VehicleModelSubscriptionDto vehicleModelSubscriptionDto) {
        VehicleModelSubscriptionDto updatedVehicleModelSubscriptionDto = subscriptionService.updateVehicleModelSubscription(vehicleModelSubscriptionDto);
        return ResponseEntity.ok(updatedVehicleModelSubscriptionDto);
    }

    @PutMapping("organisations")
    public ResponseEntity<OrganisationSubscriptionDto> update(@Valid @RequestBody OrganisationSubscriptionDto organisationSubscriptionDto) {
        OrganisationSubscriptionDto updatedOrganisationSubscriptionDto = subscriptionService.updateOrganisationSubscription(organisationSubscriptionDto);
        return ResponseEntity.ok(updatedOrganisationSubscriptionDto);
    }

    @PostMapping("organisations")
    public ResponseEntity<OrganisationSubscriptionDto> create(@Valid @RequestBody OrganisationSubscriptionDto organisationSubscriptionDto) {
        OrganisationSubscriptionDto savedSubscription = subscriptionService.createOrganisationSubscription(organisationSubscriptionDto);
        return ResponseEntity.ok(savedSubscription);
    }

    @PostMapping("vehicle-models")
    public ResponseEntity<VehicleModelSubscriptionDto> create(@Valid @RequestBody VehicleModelSubscriptionDto vehicleModelSubscriptionDto) {
        VehicleModelSubscriptionDto savedSubscription = subscriptionService.createVehicleModelSubscription(vehicleModelSubscriptionDto);
        return ResponseEntity.ok(savedSubscription);
    }

}
