package com.nichesolv.evahanam.common.controller;

import com.nichesolv.evahanam.common.annotations.ReadOnly;
import com.nichesolv.evahanam.common.dto.*;
import com.nichesolv.evahanam.common.enums.ParsingFieldType;
import com.nichesolv.evahanam.common.service.SubscriptionService;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/subscription-plans")
@Slf4j
@SecurityRequirement(name = "Bearer Authentication")
public class SubscriptionPlanController {

    @Autowired
    SubscriptionService subscriptionService;

    @GetMapping("/supported-parsing-fields")
    @ReadOnly
    public Map<String, List<String>> getDataParsingFields(@RequestParam Optional<ParsingFieldType> fieldType){
        return subscriptionService.getAllSupportedParsingFields(fieldType);
    }

    @GetMapping("/parsing-fields")
    @ReadOnly
    public List<DataParsingPlanDto> getParsingPlan(@RequestParam Optional<Long> id) {
        return subscriptionService.getParsingPlan(id);
    }

    @PostMapping("/parsing-fields")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public void saveParsingPlan(@RequestBody DataParsingPlanDto dataParsingPlanDto){
        subscriptionService.saveParsingPlan(dataParsingPlanDto);
    }

    @GetMapping("/combo")
    @ReadOnly
    public List<ComboPlanDto> getComboPlan(@RequestParam Optional<Long> id) {
        return subscriptionService.getComboPlan(id);
    }

    @PostMapping("/combo")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public void saveComboPlan(@RequestBody ComboPlanPostRequestDto comboPlanPostRequestDto) {
        subscriptionService.saveComboPlan(comboPlanPostRequestDto);
    }

    @PostMapping("/data-frequencies")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public void saveDataFrequencyPlan(@RequestBody @Validated DataFrequencyPlanDto dataFrequencyPlanDto) {
        subscriptionService.saveDataFrequencyPlan(dataFrequencyPlanDto);
    }

    @GetMapping("/data-frequencies")
    @ReadOnly
    public List<DataFrequencyPlanDto> getDataFrequencyPlan(@RequestParam Optional<Long> id) {
        return subscriptionService.getDataFrequencyPlan(id);
    }

}
