package com.nichesolv.evahanam.common.util;

import com.nichesolv.evahanam.exceptions.DateValidationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.management.InstanceAlreadyExistsException;
import java.time.*;
import java.time.temporal.ChronoUnit;

@Component
@Slf4j
public class DateValidationUtil {

    public void validateTime(Long time, String timeType) {
        log.info("inside the validate time");
        log.info("front end time : "+ time);
        String errorMessage = null;
        Long yearAgo = ZonedDateTime.now().minusYears(1).toInstant().toEpochMilli();
        Long now = Instant.now().toEpochMilli();
        log.info("now from backend : "+now);
        if (time < 1) {
            errorMessage = timeType + " cannot be less than 1";
        }
        if (time < yearAgo) {
            errorMessage = timeType + " cannot be older than a year";
        }
//        if (time > now) {
//            errorMessage = timeType + " cannot be greater than current time";
//        }
        if (errorMessage != null) {
            throw new DateValidationException(errorMessage);
        }
    }

}
