package com.nichesolv.evahanam.common.util;

import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.ResourceBundle;

@Service
public class EvMessageBundle {

    private final ResourceBundle bundle=ResourceBundle.getBundle("messages");

    public String getMessage(String message,Object... arguments){
        String mess= bundle.getString(message);
        return MessageFormat.format(mess,arguments);
    }
}
