package com.nichesolv.evahanam.common.util;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@Slf4j
public class RefreshMaterializedViewsUtil {
    @PersistenceContext
    private EntityManager entityManager;

    @Value("${refresh-materialized-views.view-names}")
    String views;

    @Value("${daily-refresh-materialized-views.view-names}")
    String dailyRefreshViews;

    @Scheduled(cron = "${refresh-materialized-views.cron.expression}", zone = "${refresh-materialized-views.cron.timezone}")
    @Transactional
    public void refreshMaterializedViews() {
        String[] viewNames = views.split(",");
        refreshViews(viewNames);
    }

    @Scheduled(cron = "${daily-refresh-materialized-views.cron.expression}",zone = "${refresh-materialized-views.cron.timezone}")
    @Transactional
    public void dailyRefreshMaterializedViews(){
        String[] viewNames = dailyRefreshViews.split(",");
        refreshViews(viewNames);
    }

    private void refreshViews(String[] viewNames){
        for (String viewName : viewNames) {
            try{
                String sql = String.format("REFRESH MATERIALIZED VIEW %s;", viewName);
                entityManager.createNativeQuery(sql).executeUpdate();
                log.info("Materialized view " + viewName + " refreshed successfully.");
            } catch (Exception e){
                log.error("Failed to refresh materialized view " + viewName, e);
            }
        }
    }
}
