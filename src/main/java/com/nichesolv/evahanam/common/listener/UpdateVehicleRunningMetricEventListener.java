package com.nichesolv.evahanam.common.listener;

import com.nichesolv.evahanam.common.events.VehicleEvent;
import com.nichesolv.evahanam.vehicle.enums.VehicleState;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.jpa.VehicleStatus;
import com.nichesolv.evahanam.vehicle.service.IVehicleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Component
@Slf4j
public class UpdateVehicleRunningMetricEventListener implements ApplicationListener<VehicleEvent> {



    @Autowired
    IVehicleService vehicleService;

    @Override
    public void onApplicationEvent(VehicleEvent event) {
        try {
            /*
            On Vehicle Running Event, update the vehicle running metrics and odometer in vehicle latest data
             */
            if (Optional.ofNullable(event.getState()).isPresent() && event.getState() == VehicleState.RUNNING) {
                vehicleService.updateVehicleRunningMetricsAndOdometerInVehicleLatestData(event.getImei(), event.getTime(), event.getDataFrequencyPlanDetails());
            }
        } catch (Exception exception) {
            log.error("Error processing VehicleEvent in UpdateVehicleRunningMetricEventListener", exception);
        }
    }
}
