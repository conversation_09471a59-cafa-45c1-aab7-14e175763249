package com.nichesolv.evahanam.common.listener;

import com.nichesolv.evahanam.common.events.VehicleEvent;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.jpa.VehicleStatus;
import com.nichesolv.evahanam.vehicle.jpa.VehicleStatusIdx;
import com.nichesolv.evahanam.vehicle.repository.VehicleStatusRepository;
import com.nichesolv.evahanam.vehicle.service.IVehicleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class UpdateVehicleEventMonitortListener implements ApplicationListener<VehicleEvent> {


    @Autowired
    IVehicleService vehicleService;

    @Autowired
    VehicleStatusRepository vehicleStatusRepository;


    @Override
    public void onApplicationEvent(VehicleEvent event) {
        try {
            VehicleStatus vehicleStatus = vehicleStatusRepository.getReferenceById(new VehicleStatusIdx(event.getTime(), event.getImei()));

            Vehicle vehicle = vehicleService.getVehicleByAnyId(event.getImei());
            /*
            On Vehicle Running Event, update the vehicle running metrics and odometer in vehicle latest data
             */

            //updating the vehicle_event_monitor table

            vehicleService.updateVehicleRunningEvent(vehicle, vehicleStatus);
        } catch (Exception e) {
            log.error("Error in updating the vehicle_event_monitor with message ", e);
        }

    }
}
