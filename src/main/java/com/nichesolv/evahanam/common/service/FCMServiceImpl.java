package com.nichesolv.evahanam.common.service;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.firebase.ErrorCode;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import com.google.firebase.messaging.*;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.nichesolv.evahanam.common.dto.PushNotificationRequestDto;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Optional;

@Service
public class FCMServiceImpl implements FCMService {


//    @Value("${firebase.serviceAccountKeyPath}")
//    private String firebaseConfigPath;

    @Value("${aws.accessKey}")
    private String accessKey;

    @Value("${aws.secretKey}")
    private String secretKey;

    @Value("${s3.firebase.admin.file.bucket.name}")
    String bucketName;

    @Value("${s3.firebase.admin.file.name}")
    String fileName;


    Logger logger = LoggerFactory.getLogger(FCMServiceImpl.class);

    @Override
    @PostConstruct
    public void initialize() {
        File temporaryFile = null;
        try {
            BasicAWSCredentials awsCreds = new BasicAWSCredentials(accessKey, secretKey);

            AmazonS3 client = AmazonS3ClientBuilder.standard()
                    .withRegion(Regions.AP_SOUTH_1.getName())
                    .withCredentials(new AWSStaticCredentialsProvider(awsCreds))
                    .build();
            InputStream data = client.getObject(bucketName, fileName).getObjectContent();
            FirebaseOptions options = new FirebaseOptions.Builder()
                    .setCredentials(GoogleCredentials.fromStream(data))
                    .build();
            if (FirebaseApp.getApps().isEmpty()) {
                FirebaseApp.initializeApp(options);
                logger.info("Firebase application has been initialized");
            }
        } catch (IOException e) {
            logger.error(e.getMessage());
        }
    }

    @Override
    public void sendPushNotificationRequest(PushNotificationRequestDto pushNotificationRequest) {
        if (Optional.ofNullable(pushNotificationRequest).isEmpty()) {
            return;
        }
        Notification notification = Notification
                .builder()
                .setTitle(pushNotificationRequest.getTitle())
                .setBody(pushNotificationRequest.getBody())
                .build();

        // Set up Android notification properties, including the icon
        AndroidNotification androidNotification = AndroidNotification.builder()
                .setChannelId(pushNotificationRequest.getChannelId())
                .setIcon(pushNotificationRequest.getIcon())
                .build();


        // Construct AndroidConfig object with custom notification properties
        AndroidConfig androidConfig = AndroidConfig.builder()
                .setNotification(androidNotification)
                .build();

        Message message = Message.builder()
                .setToken(pushNotificationRequest.getToken())
                .setNotification(notification)
                .setAndroidConfig(androidConfig)
                .build();

        try {
            String response = FirebaseMessaging.getInstance().send(message);
        } catch (FirebaseMessagingException e) {
            if (e.getErrorCode().equals(ErrorCode.NOT_FOUND)) {
                logger.info("device with token is not found { Base Error : {} }", e.getMessage());
            } else {
                throw new RuntimeException();
            }
        }

    }
}
