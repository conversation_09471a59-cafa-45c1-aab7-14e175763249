package com.nichesolv.evahanam.common.service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.TimeZone;

import static org.hibernate.validator.internal.util.Contracts.assertNotNull;

public  class DateUtils  {

    public static Long localDateTimeToLong(Instant localDateTime){
        assertNotNull(localDateTime,"Field must not be null");

        return localDateTime.toEpochMilli();

    }
    public static Instant longToLocalDateTime(Long timeInEpochMilli){
        assertNotNull(timeInEpochMilli,"Long Field must not be null");
        return Instant.ofEpochSecond(timeInEpochMilli);
    }
}
