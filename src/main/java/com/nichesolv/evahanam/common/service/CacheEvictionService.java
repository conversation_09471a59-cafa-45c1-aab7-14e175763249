package com.nichesolv.evahanam.common.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CacheEvictionService {
    @Autowired
    CacheManager cacheManager;

    public void evictAllCaches() {
        cacheManager.getCacheNames()
                .forEach(cacheName -> cacheManager.getCache(cacheName).clear());
    }
    @Scheduled(fixedRate = 3600000)
    public void evictAllcachesAtIntervals() {
        log.info("Caches are clearing");
        evictAllCaches();
        log.info("Caches are cleared");
    }
}
