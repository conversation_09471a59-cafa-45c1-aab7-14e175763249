package com.nichesolv.evahanam.common.service;

import com.nichesolv.evahanam.common.dto.CommentDto;
import com.nichesolv.evahanam.common.jpa.Comment;
import com.nichesolv.evahanam.common.repository.CommentRepository;
import com.nichesolv.evahanam.vehicleTests.exception.VehicleTestException;
import com.nichesolv.evahanam.vehicleTests.jpa.VehicleTest;
import com.nichesolv.evahanam.vehicle.repository.VehicleRepository;
import com.nichesolv.evahanam.vehicleTests.repository.VehicleTestRepository;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
public class CommentServiceImpl implements CommentService {

    @Autowired
    VehicleTestRepository vehicleTestRepository;

    @Autowired
    CommentRepository commentRepository;
    @Autowired
    VehicleRepository vehicleRepository;

    @Override
    public List<CommentDto> findCommentsByVehicleTestId(Long vehicleTestId) {
        VehicleTest vehicleTest = vehicleTestRepository.findById(vehicleTestId)
                .orElseThrow(() -> new VehicleTestException("Cannot find the test for this vehicle test id : " + vehicleTestId));
        List<CommentDto> commentDtoList = new ArrayList<>();
        for (Comment comment : commentRepository.findByVehicleTest(vehicleTest)) {
            commentDtoList.add(new CommentDto(comment.getKey(), comment.getValue(), comment.getExpectedValue(), comment.getVehicleTest().getId(), comment.getCommentType(), comment.getComment()));
        }
        return commentDtoList;
    }

    @Override
    @Transactional
    public CommentDto saveComment(CommentDto commentDto, CustomOrganisation organisation) {
        VehicleTest vehicleTest = vehicleTestRepository.findById(commentDto.getTestId())
                .orElseThrow(() -> new VehicleTestException("Cannot find the test for this vehicle test id : " + commentDto.getTestId()));
        Comment comment = new Comment();
        comment.setCommentType(commentDto.getCommentType());
        comment.setComment(commentDto.getComment());
        comment.setExpectedValue(commentDto.getExpectedValue());
        comment.setKey(commentDto.getKey());
        comment.setCreatedOn(LocalDateTime.now());
        comment.setVehicleTest(vehicleTest);
        comment.setValue(commentDto.getValue());
        commentRepository.save(comment);
        return new CommentDto(comment.getKey(), comment.getValue(), comment.getExpectedValue(), comment.getVehicleTest().getId(), comment.getCommentType(), comment.getComment());
    }
}
