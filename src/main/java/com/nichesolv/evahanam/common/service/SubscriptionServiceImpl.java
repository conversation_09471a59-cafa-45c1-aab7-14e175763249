package com.nichesolv.evahanam.common.service;

import com.nichesolv.evahanam.common.dto.*;
import com.nichesolv.evahanam.common.enums.ParsingFieldType;
import com.nichesolv.evahanam.common.enums.SubscriptionPlanStatus;
import com.nichesolv.evahanam.common.exception.*;
import com.nichesolv.evahanam.common.jpa.*;
import com.nichesolv.evahanam.common.repository.*;
import com.nichesolv.evahanam.common.util.EvMessageBundle;
import com.nichesolv.evahanam.evApp.exception.OrganisationNotFoundException;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicleModel.events.VehicleModelCreatedEvent;
import com.nichesolv.evahanam.vehicleModel.exception.VehicleModelException;
import com.nichesolv.evahanam.vehicleModel.jpa.VehicleModel;
import com.nichesolv.evahanam.vehicleModel.repository.VehicleModelRepository;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.repository.CustomOrganisationRepository;
import jakarta.persistence.EntityNotFoundException;
import com.nichesolv.evahanam.vehicle.events.VehicleCreatedEvent;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.weaver.ast.Or;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SubscriptionServiceImpl implements SubscriptionService {

    @Autowired
    DataParsingFieldRepository dataParsingFieldRepository;

    @Autowired
    private DataParsingPlanRepository dataParsingPlanRepository;

    @Autowired
    EvMessageBundle evMessageBundle;

    @Autowired
    ComboPlanRepository comboPlanRepository;

    @Autowired
    DataFrequencyPlanRepository dataFrequencyPlanRepository;

    @Autowired
    OrganisationSubscriptionRepository organisationSubscriptionRepository;

    @Autowired
    CustomOrganisationRepository customOrganisationRepository;

    @Autowired
    VehicleModelSubscriptionRepository vehicleModelSubscriptionRepository;

    @Autowired
    VehicleModelRepository vehicleModelRepository;

    @Autowired
    ActiveVehicleSubscriptionPlanRepository activeVehicleSubscriptionPlanRepository;

    @Override
    public Map<String, List<String>> getAllSupportedParsingFields(Optional<ParsingFieldType> fieldType) {
        List<DataParsingField> dataParsingFieldList;
        if (fieldType.isPresent()) {
            dataParsingFieldList = dataParsingFieldRepository.findByFieldType(fieldType.get());
        } else {
            dataParsingFieldList = dataParsingFieldRepository.findAll();
        }
        return getAllSupportedParsingFieldsMapper(dataParsingFieldList);
    }

    @Override
    public List<DataParsingPlanDto> getParsingPlan(Optional<Long> id) {
        List<DataParsingPlanDto> dataParsingPlanDtos;
        List<DataParsingPlan> dataParsingPlanList;
        dataParsingPlanList = id.map(aLong -> Arrays.asList(dataParsingPlanRepository.findById(aLong).orElseThrow(() -> new ParsingPlanException(evMessageBundle.getMessage("PARSING_PLAN_NOT_FOUND", aLong))))).orElseGet(() -> dataParsingPlanRepository.findAll());
        dataParsingPlanDtos = dataParsingPlansMapper(dataParsingPlanList);
        return dataParsingPlanDtos;
    }

    private List<DataParsingPlanDto> dataParsingPlansMapper(List<DataParsingPlan> dataParsingPlans) {
        List<DataParsingPlanDto> dataParsingPlanDtoList = new ArrayList<>();
        dataParsingPlans.forEach(dataParsingPlan -> {
            Set<DataParsingFieldDto> dataParsingFieldDtos = new HashSet<>();
            dataParsingPlan.getFields().forEach(dataParsingField -> {
                DataParsingFieldDto resultDataParsingFieldDto = new DataParsingFieldDto();
                BeanUtils.copyProperties(dataParsingField, resultDataParsingFieldDto);
                dataParsingFieldDtos.add(resultDataParsingFieldDto);
            });
            dataParsingPlanDtoList.add(new DataParsingPlanDto(dataParsingPlan.getId(), dataParsingPlan.getName(), dataParsingPlan.getDescription(), dataParsingPlan.getPlanStatus(), dataParsingFieldDtos));
        });
        return dataParsingPlanDtoList;
    }

    @Override
    @Transactional
    public DataParsingPlan saveParsingPlan(DataParsingPlanDto dataParsingPlanDto) {
        Optional<DataParsingPlan> exitingDataParsingPlan = dataParsingPlanRepository.findByName(dataParsingPlanDto.getName());
        DataParsingPlan dataParsingPlan = new DataParsingPlan(dataParsingPlanDto.getName(), dataParsingPlanDto.getDescription(), dataParsingPlanDto.getStatus(), null);
        exitingDataParsingPlan.ifPresent(parsingPlan -> dataParsingPlan.setId(parsingPlan.getId()));
        Set<DataParsingField> dataParsingFields = new HashSet<>();
        dataParsingPlanDto.getFields().forEach(e -> {
            DataParsingField dataParsingField = new DataParsingField(e.getFieldName(), e.getFieldType());
            Optional<DataParsingField> existingDataParsingField = dataParsingFieldRepository.findByFieldTypeAndFieldName(e.getFieldType(), e.getFieldName());
            if (existingDataParsingField.isPresent()) {
                dataParsingField = existingDataParsingField.get();
            }
            dataParsingFields.add(dataParsingField);
        });
        List<DataParsingField> savedDataParsingFields = dataParsingFieldRepository.saveAll(dataParsingFields);
        dataParsingPlan.setFields(new HashSet<>(savedDataParsingFields));
        return dataParsingPlanRepository.save(dataParsingPlan);
    }



    private Map<String, List<String>> getAllSupportedParsingFieldsMapper(List<DataParsingField> dataParsingFieldList) {
        Map<String, List<String>> supportedParsingFields = new HashMap<>();
        dataParsingFieldList.forEach(e -> {
            List<String> values = new ArrayList<>();
            if (supportedParsingFields.containsKey(e.getFieldType().name())) {
                values = supportedParsingFields.get(e.getFieldType().name());
                values.add(e.getFieldName());
            } else {
                values.add(e.getFieldName());
            }
            supportedParsingFields.put(e.getFieldType().name(), values);
        });
        return supportedParsingFields;
    }

    @Override
    public List<ComboPlanDto> getComboPlan(Optional<Long> id) {
        List<ComboPlan> comboPlans = id.map(e -> Arrays.asList(comboPlanRepository.findById(id.get()).orElseThrow(() -> new ParsingPlanException(evMessageBundle.getMessage("COMBO_PLAN_NOT_FOUND", e))))).orElseGet(() -> comboPlanRepository.findByStatus(SubscriptionPlanStatus.ACTIVE));
        return comboPlanMapper(comboPlans);
    }

    @Override
    @Transactional
    public void saveComboPlan(ComboPlanPostRequestDto comboPlanPostRequestDto) {
        Optional<ComboPlan> existingComboPlan = comboPlanRepository.findByName(comboPlanPostRequestDto.getName());
        ComboPlan comboPlan;
        DataFrequencyPlan dataFrequencyPlan = comboPlanPostRequestDto.getDataFrequencyPlanId() != null ? dataFrequencyPlanRepository.findById(comboPlanPostRequestDto.getDataFrequencyPlanId()).orElseThrow(() -> new DataFrequencyPlanException(evMessageBundle.getMessage("DATA_FREQUENCY_PLAN_NOT_FOUND", comboPlanPostRequestDto.getDataFrequencyPlanId()))) : null;
        DataParsingPlan dataParsingPlan = comboPlanPostRequestDto.getDataParsingPlanId() != null ? dataParsingPlanRepository.findById(comboPlanPostRequestDto.getDataParsingPlanId()).orElseThrow(() -> new ParsingPlanException(evMessageBundle.getMessage("PARSING_PLAN_NOT_FOUND", comboPlanPostRequestDto.getDataParsingPlanId()))) : null;
        if (existingComboPlan.isPresent()) {
            comboPlan = existingComboPlan.get();
        } else {
            comboPlan = new ComboPlan(comboPlanPostRequestDto.getName(), null, null, comboPlanPostRequestDto.getStatus());
        }
        comboPlan.setDataParsingPlan(dataParsingPlan);
        comboPlan.setDataFrequencyPlan(dataFrequencyPlan);
        comboPlanRepository.save(comboPlan);
    }

    @Transactional
    @Override
    public DataFrequencyPlan saveDataFrequencyPlan(DataFrequencyPlanDto dataFrequencyPlanDto) {
        Optional<DataFrequencyPlan> existingDataFrequencyPlan = dataFrequencyPlanRepository.findByName(dataFrequencyPlanDto.getName());
        DataFrequencyPlan dataFrequencyPlan;
        if (existingDataFrequencyPlan.isPresent()) {
            dataFrequencyPlan = existingDataFrequencyPlan.get();
            dataFrequencyPlan.setUnit(ChronoUnit.valueOf(dataFrequencyPlanDto.getUnit()));
            dataFrequencyPlan.setValue(dataFrequencyPlanDto.getValue());
        } else {
            dataFrequencyPlan = new DataFrequencyPlan(dataFrequencyPlanDto.getName(), dataFrequencyPlanDto.getValue(), ChronoUnit.valueOf(dataFrequencyPlanDto.getUnit()));
        }
        return dataFrequencyPlanRepository.save(dataFrequencyPlan);
    }

    @Override
    public List<DataFrequencyPlanDto> getDataFrequencyPlan(Optional<Long> id) {
        List<DataFrequencyPlanDto> dataFrequencyPlanDtos;
        List<DataFrequencyPlan> dataFrequencyPlans = id.map(e -> Arrays.asList(dataFrequencyPlanRepository.findById(e).orElseThrow(() -> new DataFrequencyPlanException(evMessageBundle.getMessage("DATA_FREQUENCY_PLAN_NOT_FOUND", e))))).orElseGet(() -> dataFrequencyPlanRepository.findAll());
        dataFrequencyPlanDtos = dataFrequencyPlans.stream().map(e -> new DataFrequencyPlanDto(e.getId(), e.getName(), e.getValue(), e.getUnit().name())).toList();
        return dataFrequencyPlanDtos;
    }


    @Override
    public List<OrganisationSubscriptionDto> getOrganisationSubscriptionByOrg(Long orgId) {
        try {
            CustomOrganisation customOrganisation = (CustomOrganisation) customOrganisationRepository.findById(orgId).orElseThrow(() -> new OrganisationNotFoundException(evMessageBundle.getMessage("ORG_NOT_FOUND")));
            List<OrganisationSubscription> organisationSubscriptionList = organisationSubscriptionRepository.findByOrganisation(customOrganisation);
            return organisationSubscriptionList.stream()
                    .map(this::mapOrganisationSubscriptionToDto)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Unexpected error while fetching subscriptions for orgId : {} ", orgId, e);
            throw new RuntimeException("Failed to fetch organisation subscription");
        }
    }

    private OrganisationSubscriptionDto mapOrganisationSubscriptionToDto(OrganisationSubscription organisationSubscription) {
        OrganisationSubscriptionDto dto = new OrganisationSubscriptionDto();
        dto.setId(organisationSubscription.getId());
        dto.setOrgId(organisationSubscription.getOrganisation().getId());
        dto.setComboPlanId(organisationSubscription.getComboPlan().getId());
        dto.setComboPlanName(organisationSubscription.getComboPlan().getName());
        dto.setStartDate(organisationSubscription.getStartDate().toEpochMilli());
        dto.setEndDate(organisationSubscription.getEndDate() != null
                ? organisationSubscription.getEndDate().toEpochMilli()
                : null);
        return dto;
    }

    private VehicleModelSubscriptionDto mapVehicleModelSubscriptionToDto(VehicleModelSubscription vehicleModelSubscription) {
        VehicleModelSubscriptionDto vehicleModelSubscriptionDto = new VehicleModelSubscriptionDto();
        vehicleModelSubscriptionDto.setId(vehicleModelSubscription.getId());
        vehicleModelSubscriptionDto.setVehicleModelId(vehicleModelSubscription.getVehicleModel().getId());
        vehicleModelSubscriptionDto.setComboPlanId(vehicleModelSubscription.getComboPlan().getId());
        vehicleModelSubscriptionDto.setComboPlanName(vehicleModelSubscription.getComboPlan().getName());
        vehicleModelSubscriptionDto.setStartDate(vehicleModelSubscription.getStartDate().toEpochMilli());
        vehicleModelSubscriptionDto.setEndDate(vehicleModelSubscription.getEndDate() != null ?
                vehicleModelSubscription.getEndDate().toEpochMilli() : null);
        return vehicleModelSubscriptionDto;
    }


    @Override
    public List<VehicleModelSubscriptionDto> getVehicleModelSubscriptionByVehicleModelId(Long vehicleModelId) {
        try {
            VehicleModel vehicleModel = vehicleModelRepository.findById(vehicleModelId).orElseThrow(() -> new VehicleModelException(evMessageBundle.getMessage("VEHICLE_MODEL_NOT_PRESENT", vehicleModelId)));
            List<VehicleModelSubscription> vehicleModelSubscriptionList = vehicleModelSubscriptionRepository.findByVehicleModel(vehicleModel);
            return vehicleModelSubscriptionList.stream()
                    .map(this::mapVehicleModelSubscriptionToDto)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Unexpected error while fetching vehicle model subscriptions for model ID: {}", vehicleModelId, e);
            throw new RuntimeException("Failed to fetch vehicle model subscriptions");
        }
    }


    @Override
    @Transactional
    public OrganisationSubscriptionDto updateOrganisationSubscription(OrganisationSubscriptionDto organisationSubscriptionDto) {
        try {
            CustomOrganisation customOrganisation = (CustomOrganisation) customOrganisationRepository.findById(organisationSubscriptionDto.getOrgId())
                    .orElseThrow(() -> new OrganisationNotFoundException(evMessageBundle.getMessage("ORG_NOT_FOUND")));

            OrganisationSubscription organisationSubscription = organisationSubscriptionRepository.findByOrganisationAndEndDateNull(customOrganisation)
                    .orElseThrow(() -> new OrganisationSubscriptionNotFoundException(evMessageBundle.getMessage("ORGANISATION_SUBSCRIPTION_NOT_FOUND", customOrganisation.getId())));

            ComboPlan comboPlan = comboPlanRepository.findById(organisationSubscriptionDto.getComboPlanId())
                    .orElseThrow(() -> new ParsingPlanException(evMessageBundle.getMessage("COMBO_PLAN_NOT_FOUND", organisationSubscriptionDto.getComboPlanId())));

            organisationSubscription.setEndDate(Instant.now());
            organisationSubscriptionRepository.save(organisationSubscription);
            OrganisationSubscription newOrganisationSubscription = (OrganisationSubscription) createSubscriptionWithCommonFields(new OrganisationSubscription(),
                    comboPlan, Instant.ofEpochMilli(organisationSubscriptionDto.getStartDate()));
            newOrganisationSubscription.setOrganisation(organisationSubscription.getOrganisation());
            organisationSubscriptionRepository.save(newOrganisationSubscription);

            List<ActiveVehicleSubscriptionPlan> activeVehicleSubscriptionPlanList = activeVehicleSubscriptionPlanRepository.findVehiclesByOrganisation(customOrganisation.getId());

            List<ActiveVehicleSubscriptionPlan> plansToUpdate = new ArrayList<>();
            for (ActiveVehicleSubscriptionPlan activeVehicleSubscriptionPlan : activeVehicleSubscriptionPlanList) {
                Vehicle vehicle = activeVehicleSubscriptionPlan.getVehicle();
                Long mfrOrgId = vehicle.getManufacturer().getId();
                Long ownerOrgId = vehicle.getOwner().getId();
                Long vehicleModelMfrOrgId = vehicle.getVehicleModel().getManufacturer().getId();

                if (ownerOrgId.equals(mfrOrgId)) {
                    Long dealershipId = null;
                    if (vehicle.getDealership() != null) {
                        dealershipId = vehicle.getDealership().getId();
                    }
                    if (vehicle.getDealership() == null || (dealershipId != null && dealershipId.equals(mfrOrgId))) {
                        if (vehicleModelMfrOrgId.equals(mfrOrgId)) {
                            setComboPlanDetails(activeVehicleSubscriptionPlan, comboPlan);
                            plansToUpdate.add(activeVehicleSubscriptionPlan);
                        }
                    }
                }
            }
            activeVehicleSubscriptionPlanRepository.saveAll(plansToUpdate);
            return mapOrganisationSubscriptionToDto(newOrganisationSubscription);
        } catch (Exception e) {
            log.error("Unexpected error while updating organisation subscription for orgId: {}", organisationSubscriptionDto.getOrgId(), e);
            throw new RuntimeException("Failed to update organisation subscription");
        }
    }

    @Override
    @Transactional
    public VehicleModelSubscriptionDto updateVehicleModelSubscription(VehicleModelSubscriptionDto vehicleModelSubscriptionDto) {
        try {
            VehicleModel vehicleModel = vehicleModelRepository.findById(vehicleModelSubscriptionDto.getVehicleModelId())
                    .orElseThrow(() -> new VehicleModelException(evMessageBundle.getMessage("VEHICLE_MODEL_NOT_PRESENT", vehicleModelSubscriptionDto.getVehicleModelId())));

            VehicleModelSubscription vehicleModelSubscription = vehicleModelSubscriptionRepository.findByVehicleModelAndEndDateNull(vehicleModel)
                    .orElseThrow(() -> new VehicleModelSubscriptionNotFoundException(evMessageBundle.getMessage("VEHICLE_MODEL_SUBSCRIPTION_NOT_FOUND", vehicleModel.getId())));

            ComboPlan comboPlan = comboPlanRepository.findById(vehicleModelSubscriptionDto.getComboPlanId())
                    .orElseThrow(() -> new ParsingPlanException(evMessageBundle.getMessage("COMBO_PLAN_NOT_FOUND", vehicleModelSubscriptionDto.getComboPlanId())));

            vehicleModelSubscription.setEndDate(Instant.now());
            vehicleModelSubscriptionRepository.save(vehicleModelSubscription);
            VehicleModelSubscription newVehicleModelSubscription = (VehicleModelSubscription) createSubscriptionWithCommonFields
                    (new VehicleModelSubscription(), comboPlan, Instant.ofEpochMilli(vehicleModelSubscriptionDto.getStartDate()));
            newVehicleModelSubscription.setVehicleModel(vehicleModel);
            vehicleModelSubscriptionRepository.save(newVehicleModelSubscription);

            List<ActiveVehicleSubscriptionPlan> activeVehicleSubscriptionPlanList = activeVehicleSubscriptionPlanRepository.findVehiclesByVehicleModel(vehicleModel.getId());
            List<ActiveVehicleSubscriptionPlan> plansToUpdate = new ArrayList<>();

            for (ActiveVehicleSubscriptionPlan activeVehicleSubscriptionPlan : activeVehicleSubscriptionPlanList) {
                Vehicle vehicle = activeVehicleSubscriptionPlan.getVehicle();
                Long vehicleModelMfrOrgId = vehicle.getVehicleModel().getManufacturer().getId();
                Long ownerOrgId = vehicle.getOwner().getId();

                if(ownerOrgId.equals(vehicleModelMfrOrgId))
                {
                    Long dealershipId = null;
                    if (vehicle.getDealership() != null) {
                        dealershipId = vehicle.getDealership().getId();
                    }
                    if (vehicle.getDealership() == null || (dealershipId != null && dealershipId.equals(vehicleModelMfrOrgId))) {
                        setComboPlanDetails(activeVehicleSubscriptionPlan, comboPlan);
                        plansToUpdate.add(activeVehicleSubscriptionPlan);
                    }
                }
            }
            activeVehicleSubscriptionPlanRepository.saveAll(plansToUpdate);
            return mapVehicleModelSubscriptionToDto(newVehicleModelSubscription);
        } catch (Exception e) {
            log.error("Unexpected error while updating vehicle model subscription for vehicleModelId: {}", vehicleModelSubscriptionDto.getVehicleModelId(), e);
            throw new RuntimeException("Failed to update vehicle model subscription");
        }
    }

    public Subscription createSubscriptionWithCommonFields(Subscription subscription, ComboPlan comboPlan, Instant startDate) {
        subscription.setComboPlan(comboPlan);
        subscription.setStartDate(startDate);
        subscription.setEndDate(null);
        return subscription;
    }

    @Override
    public Optional<SubscriptionDto> getSubscription(Long id) {
        try {
            return organisationSubscriptionRepository.findById(id)
                    .<SubscriptionDto>map(this::mapOrganisationSubscriptionToDto)
                    .or(() -> vehicleModelSubscriptionRepository.findById(id)
                            .<SubscriptionDto>map(this::mapVehicleModelSubscriptionToDto));
        } catch (Exception e) {
            log.error("Unexpected error occurred while fetching subscription for ID: {}", id, e);
            throw new RuntimeException("Failed to fetch subscription");
        }
    }

    @Override
    @Transactional
    public OrganisationSubscriptionDto createOrganisationSubscription(OrganisationSubscriptionDto organisationSubscriptionDto) {
        try {
            CustomOrganisation customOrganisation = (CustomOrganisation) customOrganisationRepository.findById(organisationSubscriptionDto.getOrgId())
                    .orElseThrow(() -> new OrganisationNotFoundException(evMessageBundle.getMessage("ORG_NOT_FOUND")));

            Optional<OrganisationSubscription> optOrganisationSubscription = organisationSubscriptionRepository.findByOrganisationAndEndDateNull(customOrganisation);
            if (optOrganisationSubscription.isPresent()) {
                throw new SubscriptionException(evMessageBundle.getMessage("ORGANISATION_SUBSCRIPTION_ALREADY_EXISTS", optOrganisationSubscription.get().getOrganisation().getId(), optOrganisationSubscription.get().getComboPlan().getId()));
            }

            ComboPlan comboPlan = comboPlanRepository.findById(organisationSubscriptionDto.getComboPlanId())
                    .orElseThrow(() -> new ParsingPlanException(evMessageBundle.getMessage("COMBO_PLAN_NOT_FOUND", organisationSubscriptionDto.getComboPlanId())));

            OrganisationSubscription organisationSubscription = createOrganisationSubscription(
                    comboPlan, Instant.ofEpochMilli(organisationSubscriptionDto.getStartDate()), customOrganisation);

            OrganisationSubscription saveOrganisationSubscription = organisationSubscriptionRepository.save(organisationSubscription);
            return mapOrganisationSubscriptionToDto(saveOrganisationSubscription);
        } catch (Exception e) {
            log.error("Unexpected error while creating organisation subscription for orgId: {}", organisationSubscriptionDto.getOrgId(), e);
            throw new RuntimeException("Failed to create organisation subscription");
        }
    }

    private OrganisationSubscription createOrganisationSubscription(ComboPlan comboPlan, Instant startDate, CustomOrganisation organisation) {
        OrganisationSubscription subscription = new OrganisationSubscription();
        subscription.setComboPlan(comboPlan);
        subscription.setStartDate(startDate);
        subscription.setEndDate(null);
        subscription.setOrganisation(organisation);
        return subscription;
    }


    @Override
    @Transactional
    public VehicleModelSubscriptionDto createVehicleModelSubscription(VehicleModelSubscriptionDto vehicleModelSubscriptionDto) {
        try {
            VehicleModel vehicleModel = vehicleModelRepository.findById(vehicleModelSubscriptionDto.getVehicleModelId())
                    .orElseThrow(() -> new VehicleModelException(evMessageBundle.getMessage("VEHICLE_MODEL_NOT_PRESENT", vehicleModelSubscriptionDto.getVehicleModelId())));

            Optional<VehicleModelSubscription> optionalVehicleModelSubscription = vehicleModelSubscriptionRepository.findByVehicleModelAndEndDateNull(vehicleModel);
            if (optionalVehicleModelSubscription.isPresent()) {
                throw new SubscriptionException(evMessageBundle.getMessage("VEHICLE_MODEL_SUBSCRIPTION_ALREADY_EXISTS", optionalVehicleModelSubscription.get().getVehicleModel().getId(), optionalVehicleModelSubscription.get().getComboPlan().getId()));
            }

            ComboPlan comboPlan = comboPlanRepository.findById(vehicleModelSubscriptionDto.getComboPlanId())
                    .orElseThrow(() -> new ParsingPlanException(evMessageBundle.getMessage("COMBO_PLAN_NOT_FOUND", vehicleModelSubscriptionDto.getComboPlanId())));

            VehicleModelSubscription vehicleModelSubscription = createVehicleModelSubscription(
                    comboPlan, Instant.ofEpochMilli(vehicleModelSubscriptionDto.getStartDate()), vehicleModel);

            VehicleModelSubscription saveVehicleModelSubscription = vehicleModelSubscriptionRepository.save(vehicleModelSubscription);
            //map to Dto
            return mapVehicleModelSubscriptionToDto(saveVehicleModelSubscription);
        } catch (Exception e) {
            log.error("Unexpected error while creating vehicle model subscription for vehicleModelId: {}", vehicleModelSubscriptionDto.getVehicleModelId(), e);
            throw new RuntimeException("Failed to create vehicle model subscription");
        }
    }

    private VehicleModelSubscription createVehicleModelSubscription(ComboPlan comboPlan, Instant startDate, VehicleModel vehicleModel) {
        VehicleModelSubscription subscription = new VehicleModelSubscription();
        subscription.setComboPlan(comboPlan);
        subscription.setStartDate(startDate);
        subscription.setEndDate(null);
        subscription.setVehicleModel(vehicleModel);
        return subscription;
    }

    private List<ComboPlanDto> comboPlanMapper(List<ComboPlan> comboPlans) {
        return comboPlans.stream().map(comboPlan -> new ComboPlanDto(comboPlan.getId(), comboPlan.getName(), comboPlan.getStatus(), null, null)).toList();
    }

    @EventListener
    @Transactional
    public void handleVehicleCreated(VehicleCreatedEvent event) {
        try {
            Vehicle vehicle = event.getVehicle();
            Optional<ActiveVehicleSubscriptionPlan> activeVehicleSubscriptionPlanOpt =
                    activeVehicleSubscriptionPlanRepository.findByVehicle(vehicle);
            if (activeVehicleSubscriptionPlanOpt.isEmpty()) {
                ActiveVehicleSubscriptionPlan activeVehicleSubscriptionPlan = new ActiveVehicleSubscriptionPlan();
                activeVehicleSubscriptionPlan.setVehicle(vehicle);
                boolean isSubscriptionCreated = false;

                // Check for vehicle model subscription
                Optional<VehicleModelSubscription> vehicleModelSubscriptionOptional =
                        vehicleModelSubscriptionRepository.findByVehicleModelAndEndDateNull(vehicle.getVehicleModel());
                if (vehicleModelSubscriptionOptional.isPresent()) {
                    VehicleModelSubscription vehicleModelSubscription = vehicleModelSubscriptionOptional.get();
                    setComboPlanDetails(activeVehicleSubscriptionPlan, vehicleModelSubscription.getComboPlan());
                    isSubscriptionCreated = true;
                    log.info("Setting subscription plan for vehicle {} from vehicle model subscription", vehicle.getId());
                } else {
                    // If no vehicle model subscription, check organisation subscription
                    Optional<OrganisationSubscription> organisationSubscriptionOptional =
                            organisationSubscriptionRepository.findByOrganisationAndEndDateNull((CustomOrganisation) vehicle.getManufacturer());
                    if (organisationSubscriptionOptional.isPresent()) {
                        setComboPlanDetails(activeVehicleSubscriptionPlan, organisationSubscriptionOptional.get().getComboPlan());
                        isSubscriptionCreated = true;
                        log.info("Setting subscription plan for vehicle {} from organisation subscription", vehicle.getId());
                    } else {
                        String errorMessage = evMessageBundle.getMessage("NO_SUBSCRIPTION_FOUND", vehicle.getId(), vehicle.getVehicleModel().getName(), vehicle.getManufacturer().getOrganisationProfile().getName());
                        log.warn("No subscription found for vehicle model or manufacturer");
                        throw new SubscriptionNotFoundException(errorMessage);
                    }
                }
                if (isSubscriptionCreated) {
                    activeVehicleSubscriptionPlanRepository.save(activeVehicleSubscriptionPlan);
                    log.info("Successfully saved active vehicle subscription plan");
                }
            }
        } catch (Exception ex){
            log.error("Error occurred while processing vehicle subscription {}",event.getVehicle().getId(),ex);
        }
    }

    private void setComboPlanDetails(ActiveVehicleSubscriptionPlan activeVehicleSubscriptionPlan, ComboPlan comboPlan) {
        activeVehicleSubscriptionPlan.setComboPlan(comboPlan);
        activeVehicleSubscriptionPlan.setDataParsingPlan(comboPlan.getDataParsingPlan());
        activeVehicleSubscriptionPlan.setDataFrequencyPlan(comboPlan.getDataFrequencyPlan());
    }

    @EventListener
    @Transactional
    public void handleVehicleModelCreation(VehicleModelCreatedEvent event) {
        try {
            VehicleModel vehicleModel = event.getVehicleModel();
            Optional<OrganisationSubscription> optOrganisationSubscription = organisationSubscriptionRepository.findByOrganisationAndEndDateNull((CustomOrganisation) vehicleModel.getManufacturer());
            if (optOrganisationSubscription.isEmpty()) {
                OrganisationSubscription organisationSubscription = new OrganisationSubscription();
                organisationSubscription.setOrganisation((CustomOrganisation) vehicleModel.getManufacturer());
                Optional<ComboPlan> comboPlanOpt = comboPlanRepository.findByName("Gold package + High Frequency");
                organisationSubscription.setComboPlan(comboPlanOpt.get());
                organisationSubscription.setStartDate(Instant.now());
                organisationSubscription.setEndDate(null);
                organisationSubscriptionRepository.save(organisationSubscription);
            }
        } catch (Exception e) {
            throw new RuntimeException("Error adding to organisation subscription ");
        }
    }

}
