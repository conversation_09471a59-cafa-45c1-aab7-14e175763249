package com.nichesolv.evahanam.common.service;

import com.nichesolv.evahanam.common.dto.*;
import com.nichesolv.evahanam.common.enums.ParsingFieldType;
import com.nichesolv.evahanam.common.jpa.*;
import com.nichesolv.usermgmt.user.model.organisation.Organisation;

import java.util.List;
import java.util.Map;
import java.util.Optional;


public interface SubscriptionService {

    public Map<String, List<String>> getAllSupportedParsingFields(Optional<ParsingFieldType> fieldType);

    public List<DataParsingPlanDto> getParsingPlan(Optional<Long> id);

    public DataParsingPlan saveParsingPlan(DataParsingPlanDto dataParsingPlanDto);

    public List<ComboPlanDto> getComboPlan(Optional<Long> id);

    public void saveComboPlan(ComboPlanPostRequestDto comboPlanPostRequestDto);

    public DataFrequencyPlan saveDataFrequencyPlan(DataFrequencyPlanDto dataFrequencyPlanDto);

    List<DataFrequencyPlanDto> getDataFrequencyPlan(Optional<Long> id);

    List<OrganisationSubscriptionDto> getOrganisationSubscriptionByOrg(Long orgId);

    List<VehicleModelSubscriptionDto> getVehicleModelSubscriptionByVehicleModelId(Long vehicleModelId);

    OrganisationSubscriptionDto updateOrganisationSubscription(OrganisationSubscriptionDto organisationSubscriptionDto);

    VehicleModelSubscriptionDto updateVehicleModelSubscription(VehicleModelSubscriptionDto vehicleModelSubscriptionDto);

    Optional<SubscriptionDto> getSubscription(Long id);

    OrganisationSubscriptionDto createOrganisationSubscription(OrganisationSubscriptionDto organisationSubscriptionDto);

    VehicleModelSubscriptionDto createVehicleModelSubscription(VehicleModelSubscriptionDto vehicleModelSubscriptionDto);
}
