package com.nichesolv.evahanam.common.service;

import com.nichesolv.evahanam.common.repository.MyCustomOrganisationRepository;
import com.nichesolv.evahanam.evApp.exception.OrganisationNotFoundException;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import com.nichesolv.nds.model.user.CustomUser;
import com.nichesolv.nds.repository.CustomOrganisationProfileRepository;
import com.nichesolv.usermgmt.user.model.organisation.OrganisationProfile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
public class CustomOrganisationService implements ICustomOrganisationService {


    @Autowired
    MyCustomOrganisationRepository myCustomOrganisationRepository;
    @Autowired
    CustomOrganisationProfileRepository customOrganisationProfileRepository;
    @Override
    public Optional<CustomOrganisation> findOrganisationByEmail(String email){
        log.info("incoming email id {}",email);
        OrganisationProfile<CustomUser> organisationProfile = customOrganisationProfileRepository
                .findByEmail(email)
                .orElseThrow(()->new OrganisationNotFoundException("Organisation profile not found"));
        return myCustomOrganisationRepository.findByOrganisationProfileId(
                organisationProfile.getId());
    }
}
