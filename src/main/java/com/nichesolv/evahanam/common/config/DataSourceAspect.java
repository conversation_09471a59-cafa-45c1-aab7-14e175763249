package com.nichesolv.evahanam.common.config;

import com.nichesolv.evahanam.common.enums.DataSourceType;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class DataSourceAspect {

    @Before("@annotation(com.nichesolv.evahanam.common.annotations.ReadOnly) || @within(com.nichesolv.evahanam.common.annotations.ReadOnly)")
    public void setReadDataSource() {
        DataSourceContextHolder.setDataSourceType(DataSourceType.READ);
    }

    @Before("@annotation(com.nichesolv.evahanam.common.annotations.ReadWrite) || @within(com.nichesolv.evahanam.common.annotations.ReadWrite)")
    public void setWriteDataSource() {
        DataSourceContextHolder.setDataSourceType(DataSourceType.PRIMARY);
    }

    //clearing datasource after read operation
    @After("@annotation(com.nichesolv.evahanam.common.annotations.ReadOnly) || @within(com.nichesolv.evahanam.common.annotations.ReadOnly)")
    public void clearDataSource() {
        DataSourceContextHolder.clearDataSourceType();
    }
}
