package com.nichesolv.evahanam.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.SimpleAsyncTaskExecutor;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

//@EnableScheduling
@Configuration
@EnableAsync
public class AsyncConfigurer {//implements SchedulingConfigurer {
    @Bean(name = "saveTelemetryExecutor")
    public Executor threadPoolTaskExecutor() {
        return Executors.newFixedThreadPool(20);
    }

    @Bean(name = "saveLocationData")
    public Executor locationThreadPoolTaskExecutor() {
        return Executors.newFixedThreadPool(20);
    }
//    @Bean(name = "scheduleExecutor")
//    public Executor schedulerExecutor() {
//        return Executors.newFixedThreadPool(2);
//    }
//
//    @Override
//    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
//        taskRegistrar.setScheduler(schedulerExecutor());
//    }
}
