package com.nichesolv.evahanam.common.config;

import com.nichesolv.evahanam.vehicleTests.enums.TestStatus;
import com.nichesolv.evahanam.vehicleTests.enums.TestTypeName;
import com.nichesolv.evahanam.vehicleTests.jpa.VehicleTest;
import com.nichesolv.evahanam.vehicleTests.repository.TestTypeRepository;
import com.nichesolv.evahanam.vehicleTests.repository.VehicleTestRepository;
import com.nichesolv.evahanam.trip.enums.TestRideSummaryPopulationStatus;
import com.nichesolv.evahanam.trip.jpa.Trip;
import com.nichesolv.evahanam.trip.repository.TripRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class StopTestRideScheduler {
    @Autowired
    VehicleTestRepository vehicleTestRepository;

    @Autowired
    TripRepository tripRepository;

    @Autowired
    TestTypeRepository testTypeRepository;


    @Scheduled(cron = "* * * * * *", zone = "IST")
    /**
     * Method to stop old test.
     * All tests which are started before T-60 minutes will be marked as completed
     * @Parqam @T is current time
     */
    public void stopRunningTests() {
        LocalDateTime testRideLocalDateTime = LocalDateTime.now().minusMinutes(1);
        LocalDateTime dynoTestLocalDateTime = LocalDateTime.now().minusMinutes(30);
//        LocalDateTime connectivityTestLocalDateTime = LocalDateTime.now().minusMinutes(2);
//        List<VehicleTest> runningTest = vehicleTestRepository
//                .findByStatusAndStartTimeIsLessThan(TestStatus.RUNNING, testRideLocalDateTime.atZone(ZoneId.systemDefault()).toInstant());

        List<VehicleTest> runningTest = new ArrayList<>();
        runningTest.addAll(vehicleTestRepository.findByStatusAndTestTypeAndCreatedOnIsLessThan(TestStatus.RUNNING, testTypeRepository.findByTestTypeName(TestTypeName.TEST_RIDE).get(), testRideLocalDateTime.atZone(ZoneId.systemDefault()).toInstant()));
        runningTest.addAll(vehicleTestRepository.findByStatusAndTestTypeAndStartTimeIsLessThan(TestStatus.RUNNING, testTypeRepository.findByTestTypeName(TestTypeName.DYNO).get(), dynoTestLocalDateTime.atZone(ZoneId.systemDefault()).toInstant()));
//        runningTest.addAll(vehicleTestRepository.findByStatusAndTestTypeAndStartTimeIsLessThan(TestStatus.RUNNING, testTypeRepository.findByTestTypeName(TestTypeName.CONNECTIVITY).get(), connectivityTestLocalDateTime.atZone(ZoneId.systemDefault()).toInstant()));

        Instant endTime;
        List<Trip> tripList = new ArrayList<>();

        for (VehicleTest vehicleTest : runningTest) {
            endTime = switch (vehicleTest.getTestType().getTestTypeName()) {
                case TEST_RIDE -> {
                    Optional<Trip> trip = tripRepository.findByVehicleTestId(vehicleTest.getId());
                    trip.ifPresent(tripList::add);
                    log.debug("manual trip list size {}", tripList.size());
                    log.debug("start time : {} , created on : {} , testRideLocalDateTime :{}", trip.get().getStartTime(), trip.get().getCreatedOn(), testRideLocalDateTime.atZone(ZoneId.systemDefault()).toInstant());
                    yield testRideLocalDateTime.plusMinutes(10).atZone(ZoneId.systemDefault()).toInstant();
                }
                case DYNO -> dynoTestLocalDateTime.plusMinutes(5).atZone(ZoneId.systemDefault()).toInstant();
                case CONNECTIVITY -> null;

            };
            vehicleTest.setStatus(TestStatus.ABORTED);
            vehicleTest.setEndTime(endTime);
            log.debug("== Test " + vehicleTest.getId() + " has been marked as aborted==");
        }
        tripList.forEach(e -> {
                e.setSummaryPopulationStatus(TestRideSummaryPopulationStatus.ABORTED);
        });
        tripRepository.saveAll(tripList);
        vehicleTestRepository.saveAll(runningTest);
    }
}