package com.nichesolv.evahanam.evApp.dto.requests;

import com.nichesolv.evahanam.common.enums.VerificationStatus;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AcceptInvite {

    @NotNull(message = "Registration number is required")
    String regNo;

    @NotNull(message = "Verification status is required")
    VerificationStatus verificationStatus;
}
