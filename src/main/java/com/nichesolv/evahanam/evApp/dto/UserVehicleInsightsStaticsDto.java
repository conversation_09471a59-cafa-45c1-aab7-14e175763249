package com.nichesolv.evahanam.evApp.dto;

import jakarta.persistence.Tuple;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserVehicleInsightsStaticsDto {
 Float distanceTravelled;
 Long rideDuration;
 Float avgSpeed;
 Double topSpeed;
 Long chargingTime;
 int numberOfSwaps;
 Map<String, Float> avgDriveModesRange;

}