package com.nichesolv.evahanam.evApp.dto;

import com.nichesolv.evahanam.common.enums.PermissionStatus;
import com.nichesolv.evahanam.common.enums.RelationType;
import com.nichesolv.evahanam.common.enums.VerificationStatus;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UserVehicleDto {

    String regNo;

    String imei;

    String ownerName;

    String riderPhoneNumber;

    String profileUrl;

    VerificationStatus verificationStatus;

    PermissionStatus permissionStatus;

    RelationType relationType;

    boolean isOwner;

    boolean isConnected;
}