package com.nichesolv.evahanam.evApp.dto;

import com.nichesolv.evahanam.vehicle.dto.ImageDto;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserVehicleLastConnectionDetailsDto {


    @NotBlank
    String regNo;

    @NotBlank
    String model;

    List<ImageDto> images;

    Integer soc;

    Float distanceCovered;

    @NotBlank
    Long startDate;

    Long endDate;
}
