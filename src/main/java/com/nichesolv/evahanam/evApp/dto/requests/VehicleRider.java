package com.nichesolv.evahanam.evApp.dto.requests;

import com.nichesolv.evahanam.common.enums.PermissionStatus;
import com.nichesolv.evahanam.common.enums.RelationType;
import com.nichesolv.evahanam.common.enums.VerificationStatus;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
public class VehicleRider {

    String regNo;

    String riderName;

    String riderPhoneNumber;

    String newRiderPhoneNumber;

    VerificationStatus verificationStatus;

    PermissionStatus permissionStatus;

    RelationType relationType;
}