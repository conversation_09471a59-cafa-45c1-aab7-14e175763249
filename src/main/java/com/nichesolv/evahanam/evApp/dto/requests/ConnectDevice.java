package com.nichesolv.evahanam.evApp.dto.requests;

import com.nichesolv.nds.dto.organisation.enums.OrganisationType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
public class ConnectDevice {
    OrganisationType organisationType;
    Long orgId;
    String chassisNumber;
    String imeiNumber;
    String code;
    Float latitude;
    Float longitude;
}
