package com.nichesolv.evahanam.evApp.dto;

import com.nichesolv.evahanam.vehicleModel.enums.DistanceUnit;
import com.nichesolv.evahanam.vehicleModel.enums.DriveMode;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class VehicleModeInfoDto {
    @NotBlank
    String mode;

    String colorCode;

    Integer range;

    DistanceUnit rangeUnit;

    Float maxRange;

    Double correction;
}