package com.nichesolv.evahanam.evApp.dto;

import com.nichesolv.evahanam.vehicle.dto.ImageDto;
import com.nichesolv.evahanam.vehicle.dto.VehicleIdentifiers;
import com.nichesolv.evahanam.vehicleModel.enums.DriveMode;
import com.nichesolv.evahanam.vehicleModel.enums.TemperatureUnit;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VehicleInfoDto extends VehicleIdentifiers {

    Integer charge;
    
    boolean isCalculatedSoc;

    @NotBlank
    boolean batteryCharging;

    @NotBlank
    boolean batteryConnected;

    Integer remainingTimeForCharge;

    @NotBlank
    String regNo;

    @NotBlank
    String modelNo;

    String color;

    String colorType;

    @NotBlank
    List<ImageDto> images;

    DriveMode currentDriveMode;

    Integer totalDistance;

    Integer batteryTemperature;

    TemperatureUnit temperatureUnit;

    Integer batteryVoltage;

    List<VehicleModeInfoDto> vehicleModeInfoList;

    Long aiVinRecordedTime;

    Float current;

    List<Float> motorDcCurrents;

    Float speed;

    String batteryName;

    boolean diMotion;
}