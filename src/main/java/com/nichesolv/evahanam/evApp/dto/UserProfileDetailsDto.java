package com.nichesolv.evahanam.evApp.dto;

import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserProfileDetailsDto {
    String firstName;
    String lastName;
    String profileImageUrl;
    Date dob;
    String sex;
    String email;
    String mobileNumber;
    String sortAddress;
    String longAddress;
    String bloodGroup;
    String emergencyNumber;
    Float weightInKg;
}
