package com.nichesolv.evahanam.evApp.dto.requests;

import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserProfile {

    @Size(max=20,message = "firstname must under 20 characters")
    String firstName;

    @Size(max=20,message = "lastname must under 20 characters")
    String lastName;

    Date dob;

    String sex;

    @Size(max=200,message = "address must under 200 characters")
    String address;

    String bloodGroup;

    Float weightInKg;

    String emergencyNumber;
}
