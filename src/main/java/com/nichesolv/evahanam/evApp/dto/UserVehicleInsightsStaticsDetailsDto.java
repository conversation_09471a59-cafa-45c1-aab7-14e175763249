package com.nichesolv.evahanam.evApp.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserVehicleInsightsStaticsDetailsDto {

 Object value;

 String dataType;

 Map<Long, Object> dataPoints;

 Map<Long, Map<String, Float>> modeRangeDataPoints;

 Map<String, Float> avgModeRange;

}