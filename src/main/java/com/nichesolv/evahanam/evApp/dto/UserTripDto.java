package com.nichesolv.evahanam.evApp.dto;

import com.nichesolv.evahanam.vehicle.dto.ImageDto;
import com.nichesolv.evahanam.vehicleModel.enums.DriveMode;
import com.nichesolv.evahanam.vehicleModel.enums.TemperatureUnit;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserTripDto {

 String regNo;

 List<ImageDto> images;

 Long startTime;

 Long endTime;

 String startAddress;

 String endAddress;

}