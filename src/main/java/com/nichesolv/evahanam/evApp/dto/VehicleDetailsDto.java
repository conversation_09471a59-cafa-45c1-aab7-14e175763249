package com.nichesolv.evahanam.evApp.dto;

import com.nichesolv.evahanam.vehicle.dto.ImageDto;
import com.nichesolv.evahanam.vehicle.dto.VehicleIdentifiers;
import com.nichesolv.evahanam.vehicleModel.enums.DriveMode;
import com.nichesolv.evahanam.vehicleModel.enums.TemperatureUnit;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;


@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VehicleDetailsDto extends VehicleIdentifiers {

    @NotBlank
    String regNo;

    @NotBlank
    String modelName;

    String vehicleManufacturerName;

    String colorName;

    String vehicleColorHexCode;

    @NotBlank
    List<ImageDto> images;

    Float odometer;

    String batteryName;

    String batteryManufacturerName;

    String fullCapacity;

    String rearTyreDiameter;

    String frontTyreDiameter;

    String tyreManufacturerName;

    Float netWeight;

    Map<String, Float> driveModesRange;

}