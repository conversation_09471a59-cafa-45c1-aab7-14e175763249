package com.nichesolv.evahanam.evApp.dto;

import com.nichesolv.evahanam.vehicle.dto.ImageDto;
import com.nichesolv.evahanam.vehicleModel.enums.DistanceUnit;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.geo.Distance;

import java.time.LocalDateTime;
import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserNearByVehiclesDto {
    @NotBlank
    String imei ;

    @NotBlank
    Float latitude;

    @NotBlank
    Float longitude;

    @NotBlank
    String regNo;

    @NotBlank
    Integer  charge;

    @NotBlank
    Integer distance;

    @NotBlank
    DistanceUnit distanceUnit;

    List<ImageDto> images;

    Long locationRecordedTime;

}