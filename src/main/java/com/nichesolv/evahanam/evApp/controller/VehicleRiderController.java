package com.nichesolv.evahanam.evApp.controller;

import com.nichesolv.evahanam.common.annotations.ReadOnly;
import com.nichesolv.evahanam.evApp.dto.requests.AcceptInvite;
import com.nichesolv.evahanam.evApp.dto.requests.VehicleRider;
import com.nichesolv.evahanam.evApp.dto.UserVehicleDto;
import com.nichesolv.evahanam.evApp.service.VehicleRiderService;
import com.nichesolv.nds.model.user.CustomUser;
import com.nichesolv.usermgmt.user.exception.UserNotFoundException;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/riders")
@SecurityRequirement(name = "Bearer Authentication")
public class VehicleRiderController {

    @Autowired
    VehicleRiderService vehicleRiderService;

    @PutMapping
    public void updateVehicleRiderAssociation(@AuthenticationPrincipal CustomUser owner, @RequestBody VehicleRider vehicleRiderBody, HttpServletRequest request) throws UserNotFoundException {

        vehicleRiderService.updateVehicleRider(owner, vehicleRiderBody, request);
    }

    @GetMapping
    @ReadOnly
    public List<UserVehicleDto> getUserRiders(@AuthenticationPrincipal CustomUser owner) {

        return vehicleRiderService.getUserRiders(owner);
    }

    @GetMapping("/vehicles")
    @ReadOnly
    public List<UserVehicleDto> getUserVehicleAndAccessedVehicles(@AuthenticationPrincipal CustomUser owner, @RequestParam(value = "orgId", required = false) Long orgId) {

        return vehicleRiderService.getUserVehicleAndAccessedVehicles(owner, orgId);
    }

    @PutMapping("/invitations/accept")
    public void acceptAccessedVehicleInvitation(@AuthenticationPrincipal CustomUser rider, @Valid @RequestBody AcceptInvite riderAcceptInviteBody) {

        vehicleRiderService.acceptAccessedVehicleInvitation(rider, riderAcceptInviteBody);
    }


}
