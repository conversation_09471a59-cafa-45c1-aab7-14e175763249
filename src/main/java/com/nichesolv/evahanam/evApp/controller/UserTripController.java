package com.nichesolv.evahanam.evApp.controller;

import com.nichesolv.evahanam.common.annotations.ReadOnly;
import com.nichesolv.evahanam.evApp.dto.UserTripDto;
import com.nichesolv.evahanam.evApp.dto.UserVehicleTestDetailDto;
import com.nichesolv.evahanam.evApp.service.IUserTripService;
import com.nichesolv.nds.dto.organisation.enums.OrganisationType;
import com.nichesolv.nds.exception.UserNotFoundException;
import com.nichesolv.nds.model.user.CustomUser;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/users")
@SecurityRequirement(name = "Bearer Authentication")
public class UserTripController {

    @Autowired
    IUserTripService iUserTripService;

    @GetMapping("/trips")
    @ReadOnly
    public List<UserTripDto> findUserTripHistory(@AuthenticationPrincipal CustomUser user,
                                                 @RequestParam(value = "organisationType", required = true) OrganisationType organisationType,
                                                 @RequestParam(value = "orgId", required = true) Long orgId,
                                                 @Validated @PageableDefault(size = 20) @SortDefault(direction = Sort.Direction.DESC, sort = "startTime") Pageable pageable)
          throws UserNotFoundException {
        return iUserTripService.getUserTripHistory(user, pageable, organisationType, orgId);
    }
}
