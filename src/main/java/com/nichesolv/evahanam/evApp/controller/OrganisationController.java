package com.nichesolv.evahanam.evApp.controller;

import com.nichesolv.evahanam.evApp.exception.OrganisationTypeException;
import com.nichesolv.evahanam.evApp.service.OrganisationService;
import com.nichesolv.evahanam.vehicle.service.IVehicleService;
import com.nichesolv.nds.controller.organisation.OrganisationControllerImpl;
import com.nichesolv.nds.dto.organisation.CustomOrganisationDto;
import com.nichesolv.nds.dto.organisation.OrgUpdateStatusDto;
import com.nichesolv.usermgmt.user.dto.security.GenericMessage;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@SecurityRequirement(name = "Bearer Authentication")
public class OrganisationController {

    @Autowired
    OrganisationService organisationService;


    @Autowired
    IVehicleService vehicleService;

    @Autowired
    OrganisationControllerImpl organisationController;
    @PostMapping({"v1/create-organisation","/organisations"})
    public ResponseEntity<?> createOrganisation(@RequestBody @Validated CustomOrganisationDto organisationDto, HttpServletRequest request){
        try{
            return organisationService.createOrganisation(organisationDto, request);
        }
        catch (OrganisationTypeException e){
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PutMapping("/organisations/status")
    @PreAuthorize("hasAnyAuthority('SUPER_ADMIN')")
    public ResponseEntity<GenericMessage> updateOrganisationStatus(@RequestBody OrgUpdateStatusDto orgStatusUpdateDto, HttpServletRequest request){
        try {
            vehicleService.updateVehicleStatus(orgStatusUpdateDto.getOrgId(), orgStatusUpdateDto.getStatus());
            organisationController.updateOrganisationStatus(orgStatusUpdateDto,request);
        }catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(new GenericMessage("Could not update organisation status", e.getLocalizedMessage()));
        }
        return ResponseEntity.ok().body(new GenericMessage("Organisation status updated successfully"));
    }
}
