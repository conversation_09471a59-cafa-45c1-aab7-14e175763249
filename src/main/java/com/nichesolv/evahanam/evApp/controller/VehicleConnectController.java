package com.nichesolv.evahanam.evApp.controller;

import com.nichesolv.evahanam.evApp.dto.ConnectVehicleDto;
import com.nichesolv.evahanam.evApp.dto.DisconnectVehicleDto;
import com.nichesolv.evahanam.evApp.dto.requests.ConnectDevice;
import com.nichesolv.evahanam.evApp.dto.requests.DisconnectDevice;
import com.nichesolv.evahanam.evApp.exception.UserProfileNotFoundException;
import com.nichesolv.evahanam.evApp.service.IUserVehicleConnectionService;
import com.nichesolv.nds.exception.UserNotFoundException;
import com.nichesolv.nds.model.user.CustomUser;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.io.FileNotFoundException;

@Slf4j
@RestController
@RequestMapping("/vehicles")
@SecurityRequirement(name = "Bearer Authentication")
public class VehicleConnectController {
    @Autowired
    IUserVehicleConnectionService iUserVehicleConnectionService;

    @PostMapping("/connect")
    public ConnectVehicleDto connectVehicle(@AuthenticationPrincipal CustomUser user, @RequestBody ConnectDevice connectDeviceBody) throws UserProfileNotFoundException, UserNotFoundException, FileNotFoundException {
       return iUserVehicleConnectionService.connectVehicle(connectDeviceBody, user);
    }

    @PostMapping("/disconnect")
    public DisconnectVehicleDto disconnectVehicle(@AuthenticationPrincipal CustomUser user, @RequestBody DisconnectDevice disconnectDeviceBody) throws UserProfileNotFoundException, UserNotFoundException {

        return iUserVehicleConnectionService.disconnectVehicle(user, disconnectDeviceBody);
    }

}
