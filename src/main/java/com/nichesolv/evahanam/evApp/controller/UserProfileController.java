package com.nichesolv.evahanam.evApp.controller;


import com.nichesolv.evahanam.common.annotations.ReadOnly;
import com.nichesolv.evahanam.evApp.dto.requests.UserProfile;
import com.nichesolv.evahanam.evApp.dto.UserInfoDto;
import com.nichesolv.evahanam.evApp.dto.UserNearByPoiDto;
import com.nichesolv.evahanam.evApp.dto.UserProfileDetailsDto;
import com.nichesolv.evahanam.evApp.service.IUserProfileService;
import com.nichesolv.nds.dto.organisation.enums.OrganisationType;
import com.nichesolv.nds.exception.UserNotFoundException;
import com.nichesolv.nds.model.user.CustomUser;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


@Slf4j
@RestController
@RequestMapping("/users")
@SecurityRequirement(name = "Bearer Authentication")
public class UserProfileController {

    @Autowired
    IUserProfileService iUserProfileService;


    @GetMapping
    @ReadOnly
    public UserInfoDto findUserProfileInfo(@AuthenticationPrincipal CustomUser sub, @RequestParam(value = "organisationType", required = true) OrganisationType organisationType, @RequestParam(value = "orgId", required = true) Long orgId) throws UserNotFoundException {
        return iUserProfileService.findByUser(sub.getId(), organisationType, orgId);
    }


    @GetMapping("/nearby-poi")
    @ReadOnly
    public UserNearByPoiDto findUserNearByPoi(@AuthenticationPrincipal CustomUser user, @RequestParam(value = "latitude", required = true) String latitude, @RequestParam(value = "longitude", required = true) String longitude, @RequestParam(value = "range", required = false) Float range, @RequestParam(value = "organisationType", required = true) OrganisationType organisationType,@RequestParam(value = "orgId", required = true) Long orgId) throws UserNotFoundException {
        return iUserProfileService.findUserNearByPoi(user, latitude, longitude, range, organisationType, orgId);
    }

    @GetMapping("/profile")
    @ReadOnly
    public UserProfileDetailsDto findUserProfileDetails() throws UserNotFoundException {
        String sub = SecurityContextHolder.getContext().getAuthentication().getName();
        return iUserProfileService.findUserProfileDetails(sub);
    }

    @PutMapping("/profile")
    public void updateUserProfileDetails(@Validated @RequestBody UserProfile updateUserProfileDetailsBody) throws UserNotFoundException {
        String sub = SecurityContextHolder.getContext().getAuthentication().getName();
        iUserProfileService.updateUserProfileDetails(sub, updateUserProfileDetailsBody);
    }

    @PutMapping("/deactivate")
    public void markUserInActive() throws UserNotFoundException {
        String sub = SecurityContextHolder.getContext().getAuthentication().getName();
        iUserProfileService.updateUserStatus(sub);
    }
}
