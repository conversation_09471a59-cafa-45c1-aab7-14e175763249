package com.nichesolv.evahanam.evApp.controller;

import com.nichesolv.evahanam.common.annotations.ReadOnly;
import com.nichesolv.evahanam.evApp.dto.VehicleDetailsDto;
import com.nichesolv.evahanam.evApp.service.IVehicleInfoService;
import com.nichesolv.evahanam.evApp.dto.VehicleInfoDto;
import com.nichesolv.evahanam.vehicle.exception.VehicleException;
import com.nichesolv.evahanam.vehicle.exception.VehicleNotFoundException;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/vehicles")
@SecurityRequirement(name = "Bearer Authentication")
public class VehicleInfoController {
    @Autowired
    IVehicleInfoService iVehicleInfoService;
    @GetMapping("/info")
    @ReadOnly
    public VehicleInfoDto getVehicleInfo(@RequestParam(value = "imei", required = false) String imei,
                                         @RequestParam(value = "timeLimitInSecondsForCurrent", required = false) Integer timeLimitInSecondsForCurrent,
                                         @RequestAttribute("identifier") String identifier,
                                         @RequestParam(value = "vIdVal",required = false) String identifierValue
                                         ) throws VehicleException, VehicleNotFoundException {

        return iVehicleInfoService.findVehicleInfo(identifier, timeLimitInSecondsForCurrent);

    }

    @GetMapping("/about")
    @ReadOnly
    public VehicleDetailsDto getVehicleDetails(@RequestParam(value = "imei", required = false) String imei,
                                               @RequestAttribute("identifier" ) String identifier,
                                               @RequestParam(value = "vIdVal",required = false) String identifierValue)  {

        return iVehicleInfoService.findVehicleDetails(identifier);

    }



}
